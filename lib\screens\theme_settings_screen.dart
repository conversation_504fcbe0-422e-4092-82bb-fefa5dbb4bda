import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_manager.dart';
import '../theme/app_theme.dart';

class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({super.key});

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        final isDark = themeManager.isDarkModeActive(context);
        
        return Scaffold(
          appBar: AppBar(
            title: const Text('إعدادات المظهر'),
            elevation: 0,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: isDark 
                    ? [AppTheme.darkBackgroundColor, AppTheme.darkSurfaceColor]
                    : [const Color(0xFFF8F9FA), const Color(0xFFE9ECEF)],
              ),
            ),
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // معاينة الثيم الحالي
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // أيقونة الثيم
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isDark ? Icons.dark_mode_rounded : Icons.light_mode_rounded,
                          size: 48,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // عنوان الثيم الحالي
                      Text(
                        _getThemeTitle(themeManager.themeMode, isDark),
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      
                      // وصف الثيم
                      Text(
                        _getThemeDescription(themeManager.themeMode, isDark),
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // خيارات الثيم
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Text(
                          'اختر المظهر',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      
                      // الوضع الفاتح
                      _buildThemeOption(
                        context: context,
                        title: 'الوضع الفاتح',
                        subtitle: 'مظهر فاتح ومشرق',
                        icon: Icons.light_mode_rounded,
                        iconColor: Colors.orange,
                        isSelected: themeManager.themeMode == ThemeMode.light,
                        onTap: () => themeManager.setLightMode(),
                      ),
                      
                      const Divider(height: 1),
                      
                      // الوضع المظلم
                      _buildThemeOption(
                        context: context,
                        title: 'الوضع المظلم',
                        subtitle: 'مظهر داكن ومريح للعين',
                        icon: Icons.dark_mode_rounded,
                        iconColor: Colors.indigo,
                        isSelected: themeManager.themeMode == ThemeMode.dark,
                        onTap: () => themeManager.setDarkMode(),
                      ),
                      
                      const Divider(height: 1),
                      
                      // وضع النظام
                      _buildThemeOption(
                        context: context,
                        title: 'تلقائي (حسب النظام)',
                        subtitle: 'يتبع إعدادات النظام',
                        icon: Icons.settings_suggest_rounded,
                        iconColor: Colors.green,
                        isSelected: themeManager.themeMode == ThemeMode.system,
                        onTap: () => themeManager.setSystemMode(),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // معلومات إضافية
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline_rounded,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'سيتم حفظ اختيارك وتطبيقه على جميع شاشات التطبيق.',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color iconColor,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: iconColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall,
      ),
      trailing: isSelected
          ? Icon(
              Icons.check_circle_rounded,
              color: Theme.of(context).primaryColor,
              size: 24,
            )
          : Icon(
              Icons.radio_button_unchecked_rounded,
              color: Theme.of(context).dividerColor,
              size: 24,
            ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    );
  }

  String _getThemeTitle(ThemeMode themeMode, bool isDark) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'الوضع الفاتح';
      case ThemeMode.dark:
        return 'الوضع المظلم';
      case ThemeMode.system:
        return isDark ? 'تلقائي (مظلم)' : 'تلقائي (فاتح)';
    }
  }

  String _getThemeDescription(ThemeMode themeMode, bool isDark) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'مظهر فاتح ومشرق مناسب للاستخدام النهاري';
      case ThemeMode.dark:
        return 'مظهر داكن ومريح للعين مناسب للاستخدام الليلي';
      case ThemeMode.system:
        return 'يتبع إعدادات النظام تلقائياً (${isDark ? 'مظلم حالياً' : 'فاتح حالياً'})';
    }
  }
}
