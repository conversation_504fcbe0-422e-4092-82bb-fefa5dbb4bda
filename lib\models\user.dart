class User {
  final String id;
  final String username;
  final String fullName;
  final String passwordHash;
  final String? email;
  final bool biometricEnabled;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  User({
    required this.id,
    required this.username,
    required this.fullName,
    required this.passwordHash,
    this.email,
    this.biometricEnabled = false,
    required this.createdAt,
    this.lastLoginAt,
  });

  // تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'fullName': fullName,
      'passwordHash': passwordHash,
      'email': email,
      'biometricEnabled': biometricEnabled ? 1 : 0,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastLoginAt': lastLoginAt?.millisecondsSinceEpoch,
    };
  }

  // إنشاء من Map (من قاعدة البيانات)
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'],
      fullName: map['fullName'],
      passwordHash: map['passwordHash'],
      email: map['email'],
      biometricEnabled: map['biometricEnabled'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      lastLoginAt: map['lastLoginAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastLoginAt'])
          : null,
    );
  }

  // نسخ مع تعديل بعض الخصائص
  User copyWith({
    String? id,
    String? username,
    String? fullName,
    String? passwordHash,
    String? email,
    bool? biometricEnabled,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      passwordHash: passwordHash ?? this.passwordHash,
      email: email ?? this.email,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  // التحقق من صحة اسم المستخدم
  static bool isValidUsername(String username) {
    if (username.length < 3 || username.length > 20) return false;
    
    // يجب أن يحتوي على أحرف وأرقام فقط
    final regex = RegExp(r'^[a-zA-Z0-9_]+$');
    return regex.hasMatch(username);
  }

  // التحقق من قوة كلمة المرور
  static PasswordStrength checkPasswordStrength(String password) {
    if (password.length < 6) return PasswordStrength.weak;
    
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    int score = 0;
    if (hasUppercase) score++;
    if (hasLowercase) score++;
    if (hasDigits) score++;
    if (hasSpecialCharacters) score++;
    if (password.length >= 8) score++;
    
    if (score >= 4) return PasswordStrength.strong;
    if (score >= 2) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  // التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    if (email.isEmpty) return true; // البريد الإلكتروني اختياري
    
    final regex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return regex.hasMatch(email);
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, fullName: $fullName, email: $email, biometricEnabled: $biometricEnabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is User &&
        other.id == id &&
        other.username == username &&
        other.fullName == fullName &&
        other.email == email &&
        other.biometricEnabled == biometricEnabled;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        username.hashCode ^
        fullName.hashCode ^
        email.hashCode ^
        biometricEnabled.hashCode;
  }
}

// تعداد قوة كلمة المرور
enum PasswordStrength {
  weak,
  medium,
  strong,
}

// امتداد للحصول على وصف قوة كلمة المرور
extension PasswordStrengthExtension on PasswordStrength {
  String get description {
    switch (this) {
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.medium:
        return 'متوسطة';
      case PasswordStrength.strong:
        return 'قوية';
    }
  }

  String get color {
    switch (this) {
      case PasswordStrength.weak:
        return 'red';
      case PasswordStrength.medium:
        return 'orange';
      case PasswordStrength.strong:
        return 'green';
    }
  }
}
