import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';
import 'package:crypto/crypto.dart';
import '../models/user.dart';
import '../database/database_helper.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  User? _currentUser;

  // الحصول على المستخدم الحالي
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // التحقق من كلمة المرور
  bool _verifyPassword(String password, String hash) {
    return _hashPassword(password) == hash;
  }

  // تسجيل مستخدم جديد
  Future<AuthResult> registerUser({
    required String username,
    required String fullName,
    required String password,
    String? email,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (!User.isValidUsername(username)) {
        return AuthResult.failure(
          'اسم المستخدم غير صحيح. يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام فقط',
        );
      }

      if (User.checkPasswordStrength(password) == PasswordStrength.weak) {
        return AuthResult.failure(
          'كلمة المرور ضعيفة. يجب أن تحتوي على 6 أحرف على الأقل',
        );
      }

      if (email != null && !User.isValidEmail(email)) {
        return AuthResult.failure('البريد الإلكتروني غير صحيح');
      }

      // التحقق من عدم وجود المستخدم
      final existingUser = await _getUserByUsername(username);
      if (existingUser != null) {
        return AuthResult.failure('اسم المستخدم موجود بالفعل');
      }

      // إنشاء المستخدم الجديد
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        username: username,
        fullName: fullName,
        passwordHash: _hashPassword(password),
        email: email?.isEmpty == true ? null : email,
        createdAt: DateTime.now(),
      );

      // حفظ في قاعدة البيانات
      await _saveUser(user);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('خطأ في إنشاء الحساب: $e');
    }
  }

  // تسجيل الدخول
  Future<AuthResult> login({
    required String username,
    required String password,
  }) async {
    try {
      final user = await _getUserByUsername(username);
      if (user == null) {
        return AuthResult.failure('اسم المستخدم غير موجود');
      }

      if (!_verifyPassword(password, user.passwordHash)) {
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }

      // تحديث آخر تسجيل دخول
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());
      await _updateUser(updatedUser);

      _currentUser = updatedUser;
      await _saveLoginSession(updatedUser);

      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('خطأ في تسجيل الدخول: $e');
    }
  }

  // تسجيل الدخول بالبصمة
  Future<AuthResult> loginWithBiometric() async {
    try {
      // التحقق من دعم البصمة
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return AuthResult.failure('البصمة غير متاحة على هذا الجهاز');
      }

      // الحصول على المستخدم المحفوظ
      final savedUser = await _getSavedBiometricUser();
      if (savedUser == null) {
        return AuthResult.failure('لم يتم تفعيل البصمة لأي مستخدم');
      }

      // طلب المصادقة بالبصمة
      final didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'استخدم بصمتك لتسجيل الدخول',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        return AuthResult.failure('فشل في التحقق من البصمة');
      }

      // تحديث آخر تسجيل دخول
      final updatedUser = savedUser.copyWith(lastLoginAt: DateTime.now());
      await _updateUser(updatedUser);

      _currentUser = updatedUser;
      await _saveLoginSession(updatedUser);

      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('خطأ في تسجيل الدخول بالبصمة: $e');
    }
  }

  // تفعيل البصمة
  Future<AuthResult> enableBiometric(String password) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('لم يتم العثور على المستخدم الحالي');
      }

      debugPrint(
        '🔐 بدء عملية تفعيل البصمة للمستخدم: ${_currentUser!.username}',
      );
      debugPrint('🔑 التحقق من كلمة المرور...');

      // التحقق من كلمة المرور
      final passwordHash = _hashPassword(password);
      debugPrint(
        '🔍 كلمة المرور المدخلة (مشفرة): ${passwordHash.substring(0, 10)}...',
      );
      debugPrint(
        '🔍 كلمة المرور المحفوظة (مشفرة): ${_currentUser!.passwordHash.substring(0, 10)}...',
      );

      if (!_verifyPassword(password, _currentUser!.passwordHash)) {
        debugPrint('❌ كلمة المرور غير صحيحة');
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }

      debugPrint('✅ تم التحقق من كلمة المرور بنجاح');

      // التحقق من دعم البصمة
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        debugPrint('❌ البصمة غير متاحة على هذا الجهاز');
        return AuthResult.failure('البصمة غير متاحة على هذا الجهاز');
      }

      debugPrint('📱 البصمة متاحة، طلب المصادقة...');

      // طلب المصادقة بالبصمة
      final didAuthenticate = await _localAuth.authenticate(
        localizedReason:
            'ضع إصبعك على مستشعر البصمة لتفعيل تسجيل الدخول بالبصمة',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        debugPrint('❌ فشل في المصادقة بالبصمة');
        return AuthResult.failure('فشل في المصادقة بالبصمة');
      }

      debugPrint('✅ تم التحقق من البصمة بنجاح');

      // تحديث المستخدم
      final updatedUser = _currentUser!.copyWith(biometricEnabled: true);
      await _updateUser(updatedUser);
      _currentUser = updatedUser;

      // حفظ معلومات البصمة
      await _saveBiometricUser(updatedUser);

      debugPrint('🎉 تم تفعيل البصمة بنجاح');
      return AuthResult.success(updatedUser);
    } catch (e) {
      debugPrint('💥 خطأ في تفعيل البصمة: $e');
      return AuthResult.failure('خطأ في تفعيل البصمة: $e');
    }
  }

  // إلغاء تفعيل البصمة
  Future<AuthResult> disableBiometric() async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('لم يتم العثور على المستخدم الحالي');
      }

      debugPrint(
        '🔓 بدء عملية إلغاء تفعيل البصمة للمستخدم: ${_currentUser!.username}',
      );

      // تحديث المستخدم
      final updatedUser = _currentUser!.copyWith(biometricEnabled: false);
      await _updateUser(updatedUser);
      _currentUser = updatedUser;

      // حذف معلومات البصمة المحفوظة
      await _removeBiometricUser();

      debugPrint('✅ تم إلغاء تفعيل البصمة بنجاح');
      return AuthResult.success(updatedUser);
    } catch (e) {
      debugPrint('💥 خطأ في إلغاء تفعيل البصمة: $e');
      return AuthResult.failure('خطأ في إلغاء تفعيل البصمة: $e');
    }
  }

  // التحقق من دعم البصمة
  Future<bool> isBiometricAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  // التحقق من وجود بصمة مفعلة
  Future<bool> hasBiometricEnabled() async {
    final savedUser = await _getSavedBiometricUser();
    return savedUser != null;
  }

  // تسجيل الخروج
  Future<void> logout() async {
    _currentUser = null;
    await _clearLoginSession();
  }

  // التحقق من الجلسة المحفوظة
  Future<bool> checkSavedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');

      if (userJson != null) {
        final userMap = json.decode(userJson);
        final savedUser = User.fromMap(userMap);

        // إعادة تحميل بيانات المستخدم من قاعدة البيانات للحصول على أحدث البيانات
        final currentUser = await _getUserById(savedUser.id);
        if (currentUser != null) {
          _currentUser = currentUser;
          // تحديث الجلسة المحفوظة بأحدث البيانات
          await _saveLoginSession(currentUser);
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من الجلسة المحفوظة: $e');
      return false;
    }
  }

  // إعادة تحميل بيانات المستخدم الحالي
  Future<void> refreshCurrentUser() async {
    if (_currentUser != null) {
      final updatedUser = await _getUserById(_currentUser!.id);
      if (updatedUser != null) {
        _currentUser = updatedUser;
        await _saveLoginSession(updatedUser);
      }
    }
  }

  // حفظ جلسة تسجيل الدخول
  Future<void> _saveLoginSession(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('current_user', json.encode(user.toMap()));
  }

  // مسح جلسة تسجيل الدخول
  Future<void> _clearLoginSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('current_user');
  }

  // حفظ معلومات البصمة
  Future<void> _saveBiometricUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('biometric_user', json.encode(user.toMap()));
  }

  // الحصول على معلومات البصمة المحفوظة
  Future<User?> _getSavedBiometricUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('biometric_user');

      if (userJson != null) {
        final userMap = json.decode(userJson);
        return User.fromMap(userMap);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // حذف معلومات البصمة
  Future<void> _removeBiometricUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('biometric_user');
  }

  // الحصول على مستخدم بواسطة اسم المستخدم
  Future<User?> _getUserByUsername(String username) async {
    final db = DatabaseHelper();
    return await db.getUserByUsername(username);
  }

  // الحصول على مستخدم بواسطة المعرف
  Future<User?> _getUserById(String id) async {
    final db = DatabaseHelper();
    return await db.getUserById(id);
  }

  // حفظ مستخدم في قاعدة البيانات
  Future<void> _saveUser(User user) async {
    final db = DatabaseHelper();
    await db.insertUser(user);
  }

  // تحديث مستخدم في قاعدة البيانات
  Future<void> _updateUser(User user) async {
    final db = DatabaseHelper();
    await db.updateUser(user);
  }
}

// نتيجة عملية المصادقة
class AuthResult {
  final bool success;
  final String? message;
  final User? user;

  AuthResult._({required this.success, this.message, this.user});

  factory AuthResult.success(User user) {
    return AuthResult._(success: true, user: user);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(success: false, message: message);
  }
}
