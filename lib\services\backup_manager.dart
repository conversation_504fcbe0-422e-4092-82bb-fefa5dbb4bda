import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../database/simple_storage.dart';
import '../services/settings_manager.dart';
import '../services/security_manager.dart';

class BackupManager {
  static final BackupManager _instance = BackupManager._internal();
  factory BackupManager() => _instance;
  BackupManager._internal();

  final SimpleStorage _storage = SimpleStorage();
  final SettingsManager _settingsManager = SettingsManager();
  final SecurityManager _securityManager = SecurityManager();

  // Auto backup functionality
  bool _autoBackupEnabled = false;
  DateTime? _lastBackupTime;
  
  bool get autoBackupEnabled => _autoBackupEnabled;
  DateTime? get lastBackupTime => _lastBackupTime;

  void enableAutoBackup() {
    _autoBackupEnabled = true;
  }

  void disableAutoBackup() {
    _autoBackupEnabled = false;
  }

  // Create comprehensive backup
  Future<Map<String, dynamic>> createFullBackup({bool includeSettings = true}) async {
    try {
      final backup = await _storage.createBackup();
      
      if (includeSettings) {
        backup['settings'] = _settingsManager.settings.toMap();
      }

      backup['backup_info'] = {
        'version': '2.0',
        'created_at': DateTime.now().toIso8601String(),
        'app_version': '1.0.0',
        'platform': kIsWeb ? 'web' : Platform.operatingSystem,
        'backup_type': 'full',
        'includes_settings': includeSettings,
      };

      _lastBackupTime = DateTime.now();
      
      _securityManager.logSecurityEvent(
        SecurityEventType.dataExport,
        'تم إنشاء نسخة احتياطية كاملة',
      );

      return backup;
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية: $e');
    }
  }

  // Create encrypted backup
  Future<String> createEncryptedBackup(String password) async {
    try {
      final backup = await createFullBackup();
      final backupJson = jsonEncode(backup);
      final encryptedData = _securityManager.encryptData(backupJson, password);
      
      _securityManager.logSecurityEvent(
        SecurityEventType.dataExport,
        'تم إنشاء نسخة احتياطية مشفرة',
      );

      return encryptedData;
    } catch (e) {
      throw Exception('فشل في تشفير النسخة الاحتياطية: $e');
    }
  }

  // Restore from backup
  Future<bool> restoreFromBackup(Map<String, dynamic> backupData) async {
    try {
      // Validate backup format
      if (!_validateBackupFormat(backupData)) {
        throw Exception('تنسيق النسخة الاحتياطية غير صحيح');
      }

      // Restore data
      final success = await _storage.restoreFromBackup(backupData);
      
      if (success && backupData.containsKey('settings')) {
        // Restore settings if included
        final settingsMap = backupData['settings'] as Map<String, dynamic>;
        await _settingsManager.importSettings(jsonEncode(settingsMap));
      }

      if (success) {
        _securityManager.logSecurityEvent(
          SecurityEventType.dataImport,
          'تم استرجاع النسخة الاحتياطية بنجاح',
        );
      }

      return success;
    } catch (e) {
      _securityManager.logSecurityEvent(
        SecurityEventType.dataImport,
        'فشل في استرجاع النسخة الاحتياطية: $e',
      );
      return false;
    }
  }

  // Restore from encrypted backup
  Future<bool> restoreFromEncryptedBackup(String encryptedData, String password) async {
    try {
      final decryptedJson = _securityManager.decryptData(encryptedData, password);
      if (decryptedJson.isEmpty) {
        throw Exception('كلمة المرور غير صحيحة أو البيانات تالفة');
      }

      final backupData = jsonDecode(decryptedJson) as Map<String, dynamic>;
      return await restoreFromBackup(backupData);
    } catch (e) {
      throw Exception('فشل في فك تشفير النسخة الاحتياطية: $e');
    }
  }

  // Validate backup format
  bool _validateBackupFormat(Map<String, dynamic> backup) {
    // Check required fields
    if (!backup.containsKey('version') || !backup.containsKey('created_at')) {
      return false;
    }

    // Check if accounts and transactions exist
    if (!backup.containsKey('accounts') || !backup.containsKey('transactions')) {
      return false;
    }

    return true;
  }

  // Get backup info
  Map<String, dynamic>? getBackupInfo(Map<String, dynamic> backup) {
    return backup['backup_info'] as Map<String, dynamic>?;
  }

  // Schedule auto backup
  Future<void> scheduleAutoBackup() async {
    if (!_autoBackupEnabled) return;

    final settings = _settingsManager.settings;
    if (!settings.autoBackupEnabled) return;

    final now = DateTime.now();
    final backupTime = _parseBackupTime(settings.backupTime);
    
    // Check if it's time for backup
    if (_shouldPerformBackup(now, backupTime, settings.backupFrequency)) {
      try {
        await createFullBackup();
        _lastBackupTime = now;
      } catch (e) {
        debugPrint('Auto backup failed: $e');
      }
    }
  }

  DateTime _parseBackupTime(String timeString) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  bool _shouldPerformBackup(DateTime now, DateTime backupTime, int frequencyDays) {
    // Check if we've passed the backup time today
    if (now.hour < backupTime.hour || 
        (now.hour == backupTime.hour && now.minute < backupTime.minute)) {
      return false;
    }

    // Check if enough days have passed since last backup
    if (_lastBackupTime != null) {
      final daysSinceLastBackup = now.difference(_lastBackupTime!).inDays;
      return daysSinceLastBackup >= frequencyDays;
    }

    return true; // First backup
  }

  // Export backup to file (web simulation)
  Future<String> exportBackupToFile({bool encrypted = false, String? password}) async {
    try {
      Map<String, dynamic> backup;
      String filename;

      if (encrypted && password != null) {
        final encryptedData = await createEncryptedBackup(password);
        backup = {'encrypted_data': encryptedData};
        filename = 'backup_encrypted_${DateTime.now().millisecondsSinceEpoch}.json';
      } else {
        backup = await createFullBackup();
        filename = 'backup_${DateTime.now().millisecondsSinceEpoch}.json';
      }

      final backupJson = jsonEncode(backup);
      
      // In a real app, you would save to file system or trigger download
      // For web, you could use html.AnchorElement to trigger download
      
      return backupJson;
    } catch (e) {
      throw Exception('فشل في تصدير النسخة الاحتياطية: $e');
    }
  }

  // Import backup from file content
  Future<bool> importBackupFromFile(String fileContent, {String? password}) async {
    try {
      final data = jsonDecode(fileContent) as Map<String, dynamic>;
      
      if (data.containsKey('encrypted_data')) {
        // Encrypted backup
        if (password == null) {
          throw Exception('كلمة المرور مطلوبة للنسخة المشفرة');
        }
        return await restoreFromEncryptedBackup(data['encrypted_data'], password);
      } else {
        // Regular backup
        return await restoreFromBackup(data);
      }
    } catch (e) {
      throw Exception('فشل في استيراد النسخة الاحتياطية: $e');
    }
  }

  // Get backup statistics
  Future<BackupStatistics> getBackupStatistics() async {
    try {
      final accounts = await _storage.getAllAccounts();
      final transactions = await _storage.getAllTransactions();
      
      return BackupStatistics(
        totalAccounts: accounts.length,
        totalTransactions: transactions.length,
        lastBackupTime: _lastBackupTime,
        autoBackupEnabled: _autoBackupEnabled,
        estimatedBackupSize: _estimateBackupSize(accounts.length, transactions.length),
      );
    } catch (e) {
      return BackupStatistics(
        totalAccounts: 0,
        totalTransactions: 0,
        lastBackupTime: null,
        autoBackupEnabled: false,
        estimatedBackupSize: 0,
      );
    }
  }

  int _estimateBackupSize(int accountCount, int transactionCount) {
    // Rough estimation: 500 bytes per account, 300 bytes per transaction
    return (accountCount * 500) + (transactionCount * 300);
  }

  // Cleanup old backups (if storing locally)
  Future<void> cleanupOldBackups({int keepCount = 10}) async {
    // Implementation would depend on storage method
    // For now, this is a placeholder
  }

  // Verify backup integrity
  Future<bool> verifyBackupIntegrity(Map<String, dynamic> backup) async {
    try {
      // Check format
      if (!_validateBackupFormat(backup)) return false;

      // Check data consistency
      final accounts = backup['accounts'] as List;
      final transactions = backup['transactions'] as List;

      // Verify all transactions reference valid accounts
      final accountIds = accounts.map((a) => a['id']).toSet();
      for (final transaction in transactions) {
        if (!accountIds.contains(transaction['account_id'])) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}

class BackupStatistics {
  final int totalAccounts;
  final int totalTransactions;
  final DateTime? lastBackupTime;
  final bool autoBackupEnabled;
  final int estimatedBackupSize;

  BackupStatistics({
    required this.totalAccounts,
    required this.totalTransactions,
    required this.lastBackupTime,
    required this.autoBackupEnabled,
    required this.estimatedBackupSize,
  });

  String get formattedBackupSize {
    if (estimatedBackupSize < 1024) {
      return '$estimatedBackupSize بايت';
    } else if (estimatedBackupSize < 1024 * 1024) {
      return '${(estimatedBackupSize / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(estimatedBackupSize / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }
}
