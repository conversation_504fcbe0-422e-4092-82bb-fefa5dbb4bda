import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeManager extends ChangeNotifier {
  static final ThemeManager _instance = ThemeManager._internal();
  factory ThemeManager() => _instance;
  ThemeManager._internal();

  static const String _themeKey = 'theme_mode';
  
  ThemeMode _themeMode = ThemeMode.system;
  bool _isDarkMode = false;

  ThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;

  // تحميل إعدادات الثيم المحفوظة
  Future<void> loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTheme = prefs.getString(_themeKey);
      
      if (savedTheme != null) {
        switch (savedTheme) {
          case 'light':
            _themeMode = ThemeMode.light;
            _isDarkMode = false;
            break;
          case 'dark':
            _themeMode = ThemeMode.dark;
            _isDarkMode = true;
            break;
          case 'system':
          default:
            _themeMode = ThemeMode.system;
            _isDarkMode = false;
            break;
        }
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الثيم: $e');
    }
  }

  // حفظ إعدادات الثيم
  Future<void> _saveTheme(String theme) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, theme);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات الثيم: $e');
    }
  }

  // تبديل إلى الوضع الفاتح
  Future<void> setLightMode() async {
    _themeMode = ThemeMode.light;
    _isDarkMode = false;
    await _saveTheme('light');
    notifyListeners();
  }

  // تبديل إلى الوضع المظلم
  Future<void> setDarkMode() async {
    _themeMode = ThemeMode.dark;
    _isDarkMode = true;
    await _saveTheme('dark');
    notifyListeners();
  }

  // تبديل إلى وضع النظام
  Future<void> setSystemMode() async {
    _themeMode = ThemeMode.system;
    _isDarkMode = false;
    await _saveTheme('system');
    notifyListeners();
  }

  // تبديل الوضع (فاتح/مظلم)
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setDarkMode();
    } else {
      await setLightMode();
    }
  }

  // التحقق من الوضع المظلم الحالي
  bool isDarkModeActive(BuildContext context) {
    if (_themeMode == ThemeMode.system) {
      return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }
}
