import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/services/app_state_service.dart';
import 'lib/models/account.dart';
import 'lib/models/transaction.dart';

void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) => AppStateService(),
      child: const TestApp(),
    ),
  );
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test App State',
      home: const TestScreen(),
    );
  }
}

class TestScreen extends StatefulWidget {
  const TestScreen({super.key});

  @override
  State<TestScreen> createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند بدء التطبيق
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateService>().loadAllData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نظام إدارة الحالة'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AppStateService>(
        builder: (context, appState, child) {
          if (appState.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (appState.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('خطأ: ${appState.error}'),
                  ElevatedButton(
                    onPressed: () => appState.clearError(),
                    child: const Text('مسح الخطأ'),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // إحصائيات
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الإحصائيات',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text('عدد الحسابات: ${appState.totalAccounts}'),
                        Text('عدد المعاملات: ${appState.totalTransactions}'),
                        Text('إجمالي الدائن: ${appState.totalPositiveBalance.toStringAsFixed(2)}'),
                        Text('إجمالي المدين: ${appState.totalNegativeBalance.toStringAsFixed(2)}'),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // أزرار الاختبار
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _addTestAccount(appState),
                        child: const Text('إضافة حساب تجريبي'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _addTestTransaction(appState),
                        child: const Text('إضافة معاملة تجريبية'),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => appState.refresh(),
                        child: const Text('تحديث البيانات'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _clearAllData(appState),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('مسح جميع البيانات'),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // قائمة الحسابات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحسابات (${appState.accounts.length})',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: ListView.builder(
                          itemCount: appState.accounts.length,
                          itemBuilder: (context, index) {
                            final account = appState.accounts[index];
                            return Card(
                              child: ListTile(
                                title: Text(account.name),
                                subtitle: Text(
                                  'الرصيد: ${account.balance.toStringAsFixed(2)} - '
                                  'المعاملات: ${account.transactionCount}',
                                ),
                                trailing: Text(
                                  account.isPositive ? 'دائن' : 'مدين',
                                  style: TextStyle(
                                    color: account.isPositive ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                onTap: () => _showAccountTransactions(appState, account),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _addTestAccount(AppStateService appState) {
    final account = Account(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'حساب تجريبي ${DateTime.now().millisecond}',
      balance: 1000.0,
      initialBalance: 1000.0,
      initialIsPositive: true,
      transactionCount: 0,
      isPositive: true,
      createdAt: DateTime.now(),
    );

    appState.addAccount(account);
  }

  void _addTestTransaction(AppStateService appState) {
    if (appState.accounts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة حساب أولاً')),
      );
      return;
    }

    final account = appState.accounts.first;
    final transaction = Transaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      accountId: account.id,
      description: 'معاملة تجريبية ${DateTime.now().millisecond}',
      amount: 100.0,
      date: DateTime.now(),
      type: TransactionType.credit,
      notes: 'معاملة تجريبية',
    );

    appState.addTransaction(transaction);
  }

  void _clearAllData(AppStateService appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المسح'),
        content: const Text('هل أنت متأكد من مسح جميع البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              appState.clearAllData();
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showAccountTransactions(AppStateService appState, Account account) {
    final transactions = appState.getTransactionsByAccount(account.id);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('معاملات ${account.name}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              final transaction = transactions[index];
              return ListTile(
                title: Text(transaction.description),
                subtitle: Text(transaction.date.toString().substring(0, 10)),
                trailing: Text(
                  '${transaction.amount.toStringAsFixed(2)} ${transaction.type == TransactionType.credit ? '+' : '-'}',
                  style: TextStyle(
                    color: transaction.type == TransactionType.credit ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
