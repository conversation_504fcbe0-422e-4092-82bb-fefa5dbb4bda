import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/custom_currency_service.dart';
import 'currency_management_screen.dart';

/// صفحة الإعدادات الحديثة للتطبيق
class ModernSettingsScreen extends StatefulWidget {
  const ModernSettingsScreen({super.key});

  @override
  State<ModernSettingsScreen> createState() => _ModernSettingsScreenState();
}

class _ModernSettingsScreenState extends State<ModernSettingsScreen> {
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  bool _notificationsEnabled = true;
  bool _autoBackup = false;
  String _selectedLanguage = 'ar';
  String _selectedTheme = 'light';
  String _defaultCurrency = 'SAR';
  bool _biometricEnabled = false;
  bool _passwordEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final defaultCurrency = await _currencyService.getDefaultCurrencySymbol();

    setState(() {
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _autoBackup = prefs.getBool('auto_backup') ?? false;
      _selectedLanguage = prefs.getString('language') ?? 'ar';
      _selectedTheme = prefs.getString('theme') ?? 'light';
      _defaultCurrency = defaultCurrency;
      _biometricEnabled = prefs.getBool('biometric_enabled') ?? false;
      _passwordEnabled = prefs.getBool('password_enabled') ?? false;
    });
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', _notificationsEnabled);
    await prefs.setBool('auto_backup', _autoBackup);
    await prefs.setString('language', _selectedLanguage);
    await prefs.setString('theme', _selectedTheme);
    await prefs.setBool('biometric_enabled', _biometricEnabled);
    await prefs.setBool('password_enabled', _passwordEnabled);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF4A5FBF).withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const Text(
          'الإعدادات',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
      centerTitle: true,
      leading: Container(
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFD32F2F), Color(0xFFE53935)],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFD32F2F).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(
            Icons.arrow_back_rounded,
            color: Colors.white,
            size: 22,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildBody() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 20),
            // قسم الإعدادات العامة
            _buildSettingsSection(
              title: 'الإعدادات العامة',
              icon: Icons.settings_rounded,
              children: [
                _buildLanguageSetting(),
                _buildThemeSetting(),
                _buildCurrencySetting(),
              ],
            ),
            const SizedBox(height: 20),
            // قسم الإشعارات
            _buildSettingsSection(
              title: 'الإشعارات',
              icon: Icons.notifications_rounded,
              children: [_buildNotificationSetting()],
            ),
            const SizedBox(height: 20),
            // قسم الأمان والخصوصية
            _buildSettingsSection(
              title: 'الأمان والخصوصية',
              icon: Icons.security_rounded,
              children: [
                _buildBiometricSetting(),
                _buildPasswordSetting(),
                _buildLogoutSetting(),
              ],
            ),
            const SizedBox(height: 20),
            // قسم النسخ الاحتياطي
            _buildSettingsSection(
              title: 'النسخ الاحتياطي',
              icon: Icons.backup_rounded,
              children: [_buildBackupSetting(), _buildBackupActions()],
            ),
            const SizedBox(height: 20),
            // قسم معلومات التطبيق
            _buildSettingsSection(
              title: 'معلومات التطبيق',
              icon: Icons.info_rounded,
              children: [_buildAppInfo()],
            ),
            const SizedBox(height: 20),
            // أزرار الإجراءات
            _buildActionButtons(),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  /// بناء قسم إعدادات
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس القسم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
          // محتوى القسم
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }

  /// إعداد اللغة
  Widget _buildLanguageSetting() {
    return _buildSettingTile(
      title: 'اللغة',
      subtitle: _selectedLanguage == 'ar' ? 'العربية' : 'English',
      icon: Icons.language_rounded,
      trailing: DropdownButton<String>(
        value: _selectedLanguage,
        underline: const SizedBox(),
        items: const [
          DropdownMenuItem(value: 'ar', child: Text('العربية')),
          DropdownMenuItem(value: 'en', child: Text('English')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _selectedLanguage = value;
            });
            _saveSettings();
            _showSuccessMessage('تم تغيير اللغة بنجاح');
          }
        },
      ),
    );
  }

  /// إعداد الثيم
  Widget _buildThemeSetting() {
    return _buildSettingTile(
      title: 'المظهر',
      subtitle: _selectedTheme == 'light' ? 'فاتح' : 'داكن',
      icon: Icons.palette_rounded,
      trailing: DropdownButton<String>(
        value: _selectedTheme,
        underline: const SizedBox(),
        items: const [
          DropdownMenuItem(value: 'light', child: Text('فاتح')),
          DropdownMenuItem(value: 'dark', child: Text('داكن')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _selectedTheme = value;
            });
            _saveSettings();
            _showSuccessMessage('تم تغيير المظهر بنجاح');
          }
        },
      ),
    );
  }

  /// إعداد العملة
  Widget _buildCurrencySetting() {
    return _buildSettingTile(
      title: 'العملة الافتراضية',
      subtitle: _defaultCurrency,
      icon: Icons.attach_money_rounded,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CurrencyManagementScreen(),
          ),
        );
        if (result != null) {
          _loadSettings();
        }
      },
    );
  }

  /// إعداد الإشعارات
  Widget _buildNotificationSetting() {
    return _buildSettingTile(
      title: 'تفعيل الإشعارات',
      subtitle: 'استقبال إشعارات التطبيق',
      icon: Icons.notifications_rounded,
      trailing: Switch(
        value: _notificationsEnabled,
        onChanged: (value) {
          setState(() {
            _notificationsEnabled = value;
          });
          _saveSettings();
          _showSuccessMessage(
            value ? 'تم تفعيل الإشعارات' : 'تم إلغاء تفعيل الإشعارات',
          );
        },
        activeColor: const Color(0xFF4A5FBF),
      ),
    );
  }

  /// إعداد النسخ الاحتياطي
  Widget _buildBackupSetting() {
    return _buildSettingTile(
      title: 'النسخ الاحتياطي التلقائي',
      subtitle: 'إنشاء نسخة احتياطية تلقائياً',
      icon: Icons.backup_rounded,
      trailing: Switch(
        value: _autoBackup,
        onChanged: (value) {
          setState(() {
            _autoBackup = value;
          });
          _saveSettings();
          _showSuccessMessage(
            value
                ? 'تم تفعيل النسخ الاحتياطي التلقائي'
                : 'تم إلغاء النسخ الاحتياطي التلقائي',
          );
        },
        activeColor: const Color(0xFF4A5FBF),
      ),
    );
  }

  /// إعداد البصمة الحيوية
  Widget _buildBiometricSetting() {
    return _buildSettingTile(
      title: 'البصمة الحيوية',
      subtitle: 'تسجيل الدخول بالبصمة',
      icon: Icons.fingerprint_rounded,
      trailing: Switch(
        value: _biometricEnabled,
        onChanged: (value) {
          setState(() {
            _biometricEnabled = value;
          });
          _saveSettings();
          _showSuccessMessage(
            value ? 'تم تفعيل البصمة الحيوية' : 'تم إلغاء تفعيل البصمة الحيوية',
          );
        },
        activeColor: const Color(0xFF4A5FBF),
      ),
    );
  }

  /// إعداد كلمة المرور
  Widget _buildPasswordSetting() {
    return _buildSettingTile(
      title: 'حماية بكلمة مرور',
      subtitle: 'تفعيل حماية التطبيق بكلمة مرور',
      icon: Icons.lock_rounded,
      trailing: Switch(
        value: _passwordEnabled,
        onChanged: (value) {
          if (value) {
            _showPasswordSetupDialog();
          } else {
            setState(() {
              _passwordEnabled = false;
              _biometricEnabled = false; // إلغاء البصمة عند إلغاء كلمة المرور
            });
            _saveSettings();
            _showSuccessMessage('تم إلغاء حماية كلمة المرور');
          }
        },
        activeColor: const Color(0xFF4A5FBF),
      ),
    );
  }

  /// إعداد تسجيل الخروج
  Widget _buildLogoutSetting() {
    return _buildSettingTile(
      title: 'تسجيل الخروج',
      subtitle: 'الخروج من التطبيق',
      icon: Icons.logout_rounded,
      onTap: () => _showLogoutDialog(),
    );
  }

  /// إجراءات النسخ الاحتياطي
  Widget _buildBackupActions() {
    return Column(
      children: [
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                title: 'إنشاء نسخة احتياطية',
                icon: Icons.backup_table_rounded,
                color: const Color(0xFF10B981),
                onTap: () => _createBackup(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'استعادة النسخة',
                icon: Icons.restore_rounded,
                color: const Color(0xFFEF4444),
                onTap: () => _restoreBackup(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// معلومات التطبيق
  Widget _buildAppInfo() {
    return Column(
      children: [
        _buildInfoTile(
          title: 'اسم التطبيق',
          subtitle: 'دفتر الحسابات',
          icon: Icons.apps_rounded,
        ),
        const SizedBox(height: 12),
        _buildInfoTile(
          title: 'الإصدار',
          subtitle: '1.0.0',
          icon: Icons.info_rounded,
        ),
        const SizedBox(height: 12),
        _buildInfoTile(
          title: 'المطور',
          subtitle: 'فريق التطوير',
          icon: Icons.code_rounded,
        ),
      ],
    );
  }

  /// أزرار الإجراءات
  Widget _buildActionButtons() {
    return Column(
      children: [
        _buildMainActionButton(
          title: 'إعادة تعيين الإعدادات',
          subtitle: 'استعادة الإعدادات الافتراضية',
          icon: Icons.restore_rounded,
          color: const Color(0xFFEF4444),
          onTap: () => _resetSettings(),
        ),
        const SizedBox(height: 16),
        _buildMainActionButton(
          title: 'تصدير الإعدادات',
          subtitle: 'حفظ الإعدادات في ملف',
          icon: Icons.file_download_rounded,
          color: const Color(0xFF4A5FBF),
          onTap: () => _exportSettings(),
        ),
      ],
    );
  }

  /// بناء عنصر إعداد
  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required IconData icon,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF4A5FBF).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: const Color(0xFF4A5FBF), size: 20),
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: Color(0xFF1F2937),
            fontSize: 14,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(
            color: Color(0xFF6B7280),
            fontSize: 12,
            fontFamily: 'Cairo',
          ),
        ),
        trailing:
            trailing ??
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFF9CA3AF),
            ),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoTile({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF4A5FBF).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: const Color(0xFF4A5FBF), size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Color(0xFF1F2937),
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
                Text(
                  subtitle,
                  style: const TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 12,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء صغير
  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء رئيسي
  Widget _buildMainActionButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3)),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: color,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Color(0xFF6B7280),
                      fontSize: 12,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// إنشاء نسخة احتياطية
  void _createBackup() {
    _showSuccessMessage('تم إنشاء النسخة الاحتياطية بنجاح');
  }

  /// استعادة النسخة الاحتياطية
  void _restoreBackup() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'استعادة النسخة الاحتياطية',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            content: const Text(
              'هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSuccessMessage('تم استعادة النسخة الاحتياطية بنجاح');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                ),
                child: const Text(
                  'استعادة',
                  style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                ),
              ),
            ],
          ),
    );
  }

  /// إعادة تعيين الإعدادات
  void _resetSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'إعادة تعيين الإعدادات',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            content: const Text(
              'هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.clear();
                  setState(() {
                    _notificationsEnabled = true;
                    _autoBackup = false;
                    _selectedLanguage = 'ar';
                    _selectedTheme = 'light';
                  });
                  _showSuccessMessage('تم إعادة تعيين الإعدادات بنجاح');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                ),
                child: const Text(
                  'إعادة تعيين',
                  style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                ),
              ),
            ],
          ),
    );
  }

  /// تصدير الإعدادات
  void _exportSettings() {
    _showSuccessMessage('تم تصدير الإعدادات بنجاح');
  }

  /// عرض حوار إعداد كلمة المرور
  void _showPasswordSetupDialog() {
    final TextEditingController passwordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'إعداد كلمة المرور',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: passwordController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'كلمة المرور',
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(fontFamily: 'Cairo'),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: confirmPasswordController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'تأكيد كلمة المرور',
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(fontFamily: 'Cairo'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (passwordController.text.isEmpty) {
                    _showErrorMessage('يرجى إدخال كلمة المرور');
                    return;
                  }
                  if (passwordController.text !=
                      confirmPasswordController.text) {
                    _showErrorMessage('كلمة المرور غير متطابقة');
                    return;
                  }
                  if (passwordController.text.length < 4) {
                    _showErrorMessage(
                      'كلمة المرور يجب أن تكون 4 أرقام على الأقل',
                    );
                    return;
                  }

                  Navigator.pop(context);
                  setState(() {
                    _passwordEnabled = true;
                  });
                  _saveSettings();
                  _showSuccessMessage('تم إعداد كلمة المرور بنجاح');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A5FBF),
                ),
                child: const Text(
                  'حفظ',
                  style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                ),
              ),
            ],
          ),
    );
  }

  /// عرض حوار تسجيل الخروج
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            content: const Text(
              'هل أنت متأكد من رغبتك في تسجيل الخروج من التطبيق؟',
              style: TextStyle(fontFamily: 'Cairo'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'إلغاء',
                  style: TextStyle(fontFamily: 'Cairo'),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.of(context).popUntil((route) => route.isFirst);
                  _showSuccessMessage('تم تسجيل الخروج بنجاح');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                ),
                child: const Text(
                  'تسجيل الخروج',
                  style: TextStyle(color: Colors.white, fontFamily: 'Cairo'),
                ),
              ),
            ],
          ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: const Color(0xFFEF4444),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
