import 'package:flutter/material.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../theme/app_theme.dart';
import '../screens/account_options_screen.dart';
import '../services/custom_currency_service.dart';
import 'modern_card.dart';

class AccountItem extends StatefulWidget {
  final Account account;
  final VoidCallback onTap;
  final VoidCallback onAdd;
  final VoidCallback? onLongPress;
  final VoidCallback? onRefresh;

  const AccountItem({
    super.key,
    required this.account,
    required this.onTap,
    required this.onAdd,
    this.onLongPress,
    this.onRefresh,
  });

  @override
  State<AccountItem> createState() => _AccountItemState();
}

class _AccountItemState extends State<AccountItem> {
  final CustomCurrencyService _currencyService = CustomCurrencyService();
  Currency? _accountCurrency;

  @override
  void initState() {
    super.initState();
    _loadAccountCurrency();
  }

  Future<void> _loadAccountCurrency() async {
    if (widget.account.currencyId != null) {
      final currency = await _currencyService.getCurrencyById(
        widget.account.currencyId!,
      );
      if (mounted) {
        setState(() {
          _accountCurrency = currency;
        });
      }
    } else {
      // Use default currency if no currency is set
      final defaultCurrency = await _currencyService.getDefaultCurrency();
      if (mounted) {
        setState(() {
          _accountCurrency = defaultCurrency;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      onTap: widget.onTap,
      onLongPress: widget.onLongPress ?? () => _showAccountOptions(context),
      enableHoverEffect: true,
      enableGlow: true,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).cardColor,
              widget.account.isPositive
                  ? AppTheme.successColor.withValues(alpha: 0.05)
                  : AppTheme.errorColor.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Row(
          children: [
            // Enhanced Status indicator with animated design
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient:
                    widget.account.isPositive
                        ? AppTheme.successGradient
                        : AppTheme.sunsetGradient,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: (widget.account.isPositive
                            ? AppTheme.successColor
                            : AppTheme.errorColor)
                        .withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Background circle
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  // Icon
                  Icon(
                    widget.account.isPositive
                        ? Icons.trending_up_rounded
                        : Icons.trending_down_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Enhanced Account info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Account name with enhanced styling
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.account.name,
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            color:
                                Theme.of(context).textTheme.titleMedium?.color,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // Status badge moved here
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          gradient:
                              widget.account.isPositive
                                  ? AppTheme.successGradient
                                  : AppTheme.errorGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          widget.account.isPositive ? 'دائن' : 'مدين',
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Enhanced Balance with currency
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          (widget.account.isPositive
                                  ? AppTheme.successColor
                                  : AppTheme.errorColor)
                              .withValues(alpha: 0.1),
                          (widget.account.isPositive
                                  ? AppTheme.successColor
                                  : AppTheme.errorColor)
                              .withValues(alpha: 0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: (widget.account.isPositive
                                ? AppTheme.successColor
                                : AppTheme.errorColor)
                            .withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.account_balance_wallet_rounded,
                          size: 14,
                          color:
                              widget.account.isPositive
                                  ? AppTheme.successColor
                                  : AppTheme.errorColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _accountCurrency != null
                              ? widget.account.formattedBalanceWithCurrency(
                                _accountCurrency!.symbol,
                              )
                              : widget.account.formattedBalanceWithCurrency(
                                'ر.س',
                              ),
                          style: Theme.of(
                            context,
                          ).textTheme.titleSmall?.copyWith(
                            color:
                                widget.account.isPositive
                                    ? AppTheme.successColor
                                    : AppTheme.errorColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Phone number if available
                  if (widget.account.phoneNumber != null &&
                      widget.account.phoneNumber!.isNotEmpty) ...[
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            Icons.phone_rounded,
                            size: 12,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            widget.account.phoneNumber!,
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                  ],

                  // Enhanced Transaction count with icon
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          Icons.receipt_long_rounded,
                          size: 14,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${widget.account.transactionCount} معاملة',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Enhanced Action button
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                gradient: AppTheme.oceanGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: AppTheme.buttonShadow,
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                child: InkWell(
                  onTap: widget.onAdd,
                  borderRadius: BorderRadius.circular(16),
                  child: const Icon(
                    Icons.add_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAccountOptions(BuildContext context) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) =>
                AccountOptionsScreen(account: widget.account),
        transitionDuration: const Duration(milliseconds: 300),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: ScaleTransition(
              scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
              ),
              child: child,
            ),
          );
        },
      ),
    ).then((result) {
      if (result == 'updated' || result == 'deleted') {
        // Trigger refresh in parent widget
        if (widget.onRefresh != null) {
          widget.onRefresh!();
        } else {
          // Fallback to onTap if onRefresh is not provided
          widget.onTap();
        }
      }
    });
  }
}
