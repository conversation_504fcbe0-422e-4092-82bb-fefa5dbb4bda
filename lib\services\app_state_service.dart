import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';
import 'custom_currency_service.dart';

/// خدمة إدارة حالة التطبيق مع التحديث التلقائي
class AppStateService extends ChangeNotifier {
  static final AppStateService _instance = AppStateService._internal();
  factory AppStateService() => _instance;
  AppStateService._internal();

  final DatabaseHelper _storage = DatabaseHelper();

  // البيانات المحفوظة في الذاكرة
  List<Account> _accounts = [];
  List<Transaction> _transactions = [];
  bool _isLoading = false;
  String? _error;

  // Getters للوصول للبيانات
  List<Account> get accounts => List.unmodifiable(_accounts);
  List<Transaction> get transactions => List.unmodifiable(_transactions);
  bool get isLoading => _isLoading;
  String? get error => _error;

  // إحصائيات سريعة
  int get totalAccounts => _accounts.length;
  int get totalTransactions => _transactions.length;
  double get totalPositiveBalance => _accounts
      .where((account) => account.isPositive)
      .fold(0.0, (sum, account) => sum + account.balance);
  double get totalNegativeBalance => _accounts
      .where((account) => !account.isPositive)
      .fold(0.0, (sum, account) => sum + account.balance);

  /// تحميل جميع البيانات من قاعدة البيانات
  Future<void> loadAllData() async {
    _setLoading(true);
    _setError(null);

    try {
      // تهيئة خدمة العملة أولاً
      final currencyService = CustomCurrencyService();
      await currencyService.addDefaultCurrenciesIfEmpty();

      // التحقق من سلامة قاعدة البيانات أولاً
      final isHealthy = await _storage.isDatabaseHealthy();
      if (!isHealthy) {
        await _storage.repairDatabase();
      }

      // تحميل الحسابات والمعاملات بشكل متوازي
      final results = await Future.wait([
        _storage.getAllAccounts(),
        _storage.getAllTransactions(),
      ]);

      _accounts = results[0] as List<Account>;
      _transactions = results[1] as List<Transaction>;

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل البيانات: $e');
      _setLoading(false);
      notifyListeners();
    }
  }

  /// إضافة حساب جديد
  Future<bool> addAccount(Account account) async {
    try {
      await _storage.insertAccount(account);
      await _refreshAccounts();
      // حفظ فوري للتأكد من عدم فقدان البيانات
      await saveAllDataImmediately();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في إضافة الحساب: $e');
      notifyListeners();
      return false;
    }
  }

  /// تحديث حساب موجود
  Future<bool> updateAccount(Account account) async {
    try {
      await _storage.updateAccount(account);
      await _refreshAccounts();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في تحديث الحساب: $e');
      notifyListeners();
      return false;
    }
  }

  /// حذف حساب
  Future<bool> deleteAccount(String accountId) async {
    try {
      await _storage.deleteAccount(accountId);
      await _refreshAccounts();
      await _refreshTransactions();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في حذف الحساب: $e');
      notifyListeners();
      return false;
    }
  }

  /// إضافة معاملة جديدة
  Future<bool> addTransaction(Transaction transaction) async {
    try {
      await _storage.insertTransaction(transaction);
      // تحديث كل من الحسابات والمعاملات
      await _refreshAccounts();
      await _refreshTransactions();
      // حفظ فوري للتأكد من عدم فقدان البيانات
      await saveAllDataImmediately();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في إضافة المعاملة: $e');
      notifyListeners();
      return false;
    }
  }

  /// تحديث معاملة موجودة
  Future<bool> updateTransaction(Transaction transaction) async {
    try {
      await _storage.updateTransaction(transaction);
      // تحديث كل من الحسابات والمعاملات
      await _refreshAccounts();
      await _refreshTransactions();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في تحديث المعاملة: $e');
      notifyListeners();
      return false;
    }
  }

  /// حذف معاملة
  Future<bool> deleteTransaction(String transactionId) async {
    try {
      await _storage.deleteTransaction(transactionId);
      // تحديث كل من الحسابات والمعاملات
      await _refreshAccounts();
      await _refreshTransactions();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في حذف المعاملة: $e');
      notifyListeners();
      return false;
    }
  }

  /// الحصول على معاملات حساب معين
  List<Transaction> getTransactionsByAccount(String accountId) {
    return _transactions
        .where((transaction) => transaction.accountId == accountId)
        .toList();
  }

  /// الحصول على حساب بالمعرف
  Account? getAccountById(String accountId) {
    try {
      return _accounts.firstWhere((account) => account.id == accountId);
    } catch (e) {
      return null;
    }
  }

  /// البحث في الحسابات
  List<Account> searchAccounts(String query) {
    if (query.isEmpty) return _accounts;

    return _accounts
        .where(
          (account) => account.name.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();
  }

  /// البحث في المعاملات
  List<Transaction> searchTransactions(String query) {
    if (query.isEmpty) return _transactions;

    return _transactions
        .where(
          (transaction) =>
              transaction.description.toLowerCase().contains(
                query.toLowerCase(),
              ) ||
              (transaction.notes?.toLowerCase().contains(query.toLowerCase()) ??
                  false),
        )
        .toList();
  }

  /// إعادة حساب جميع الأرصدة
  Future<void> recalculateAllBalances() async {
    try {
      await _storage.recalculateAllBalances();
      await _refreshAccounts();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في إعادة حساب الأرصدة: $e');
      notifyListeners();
    }
  }

  /// تحديث الحسابات من قاعدة البيانات
  Future<void> _refreshAccounts() async {
    _accounts = await _storage.getAllAccounts();
  }

  /// تحديث المعاملات من قاعدة البيانات
  Future<void> _refreshTransactions() async {
    _transactions = await _storage.getAllTransactions();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// تعيين رسالة الخطأ
  void _setError(String? error) {
    _error = error;
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// تحديث فوري لجميع البيانات
  Future<void> refresh() async {
    await loadAllData();
  }

  /// مسح جميع البيانات
  Future<bool> clearAllData() async {
    try {
      await _storage.clearAllData();
      _accounts.clear();
      _transactions.clear();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('خطأ في مسح البيانات: $e');
      notifyListeners();
      return false;
    }
  }

  /// حفظ فوري لجميع البيانات (للتأكد من عدم فقدانها)
  Future<void> saveAllDataImmediately() async {
    try {
      // التأكد من أن جميع البيانات محفوظة في قاعدة البيانات
      await _storage.recalculateAllBalances();

      // التحقق من سلامة قاعدة البيانات
      final isHealthy = await _storage.isDatabaseHealthy();
      if (!isHealthy) {
        await _storage.repairDatabase();
      }
    } catch (e) {
      _setError('خطأ في حفظ البيانات: $e');
      notifyListeners();
    }
  }

  /// التحقق من وجود بيانات في قاعدة البيانات
  Future<bool> hasData() async {
    try {
      final accounts = await _storage.getAllAccounts();
      return accounts.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
