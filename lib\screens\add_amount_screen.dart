import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/currency.dart';
import '../services/app_state_service.dart';
import '../services/custom_currency_service.dart';
import '../services/attachment_service.dart';
import '../widgets/attachment_picker.dart';
import '../theme/modern_app_theme.dart';
import '../widgets/modern_ui_components.dart';

/// شاشة إضافة معاملة جديدة بتصميم عصري ومبتكر
class AddAmountScreen extends StatefulWidget {
  final Account account;

  const AddAmountScreen({super.key, required this.account});

  @override
  State<AddAmountScreen> createState() => _AddAmountScreenState();
}

class _AddAmountScreenState extends State<AddAmountScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  TransactionType _selectedType = TransactionType.credit;

  AttachmentResult? _selectedAttachment;
  Currency? _selectedCurrency;
  List<Currency> _availableCurrencies = [];

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadAccountCurrency();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  Future<void> _loadAccountCurrency() async {
    try {
      final currencies = await _currencyService.getAllCurrencies();
      final defaultCurrency = await _currencyService.getDefaultCurrency();

      // Remove duplicate currencies based on code
      final uniqueCurrencies = <Currency>[];
      final seenCodes = <String>{};

      for (final currency in currencies) {
        if (!seenCodes.contains(currency.code)) {
          seenCodes.add(currency.code);
          uniqueCurrencies.add(currency);
        }
      }

      if (mounted) {
        setState(() {
          _availableCurrencies = uniqueCurrencies;

          if (defaultCurrency != null && uniqueCurrencies.isNotEmpty) {
            // البحث عن العملة المطابقة
            Currency? matchingCurrency;
            try {
              matchingCurrency = uniqueCurrencies.firstWhere(
                (c) => c.code == defaultCurrency.code,
              );
            } catch (e) {
              // إذا لم توجد العملة المطابقة، استخدم الأولى
              matchingCurrency = uniqueCurrencies.first;
            }
            _selectedCurrency = matchingCurrency;
          } else if (uniqueCurrencies.isNotEmpty) {
            _selectedCurrency = uniqueCurrencies.first;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ModernUIComponents.showModernSnackBar(
          context: context,
          message: 'خطأ في تحميل العملات: $e',
          isError: true,
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: ModernAppTheme.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: ModernAppTheme.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final isNewAccount = widget.account.balance == 0.0 &&
          widget.account.transactionCount == 0;

      final transaction = Transaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        accountId: widget.account.id,
        description: _descriptionController.text.trim().isEmpty
            ? (isNewAccount
                ? (_selectedType == TransactionType.credit
                    ? 'رصيد افتتاحي - دائن'
                    : 'رصيد افتتاحي - مدين')
                : (_selectedType == TransactionType.credit ? 'إيداع' : 'سحب'))
            : _descriptionController.text.trim(),
        amount: double.parse(_amountController.text),
        date: _selectedDate,
        type: _selectedType,
        notes: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        currencyCode: _selectedCurrency?.code ?? 'SAR',
        currencySymbol: _selectedCurrency?.symbol ?? 'SAR',
        attachmentPath: _selectedAttachment?.path,
        attachmentType: _selectedAttachment?.type,
        attachmentName: _selectedAttachment?.name,
      );

      final success = await context.read<AppStateService>().addTransaction(
        transaction,
      );

      if (mounted) {
        if (success) {
          Navigator.pop(context, true);
          ModernUIComponents.showModernSnackBar(
            context: context,
            message: _selectedType == TransactionType.credit
                ? 'تم إضافة الإيداع بنجاح'
                : 'تم إضافة السحب بنجاح',
            isSuccess: true,
          );
        } else {
          ModernUIComponents.showModernSnackBar(
            context: context,
            message: 'فشل في إضافة المعاملة. يرجى المحاولة مرة أخرى.',
            isError: true,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ModernUIComponents.showModernSnackBar(
          context: context,
          message: 'خطأ في إضافة المعاملة: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isNewAccount = widget.account.balance == 0.0 &&
        widget.account.transactionCount == 0;

    return Scaffold(
      backgroundColor: ModernAppTheme.backgroundColor,
      appBar: _buildModernAppBar(isNewAccount),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              ModernAppTheme.backgroundColor,
              Color(0xFFF1F5F9),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isNewAccount),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء شريط التطبيق الحديث
  PreferredSizeWidget _buildModernAppBar(bool isNewAccount) {
    return ModernUIComponents.modernAppBar(
      title: isNewAccount ? 'إضافة المعاملة الافتتاحية' : 'إضافة معاملة',
      gradientColors: const [
        ModernAppTheme.primaryColor,
        ModernAppTheme.primaryLight,
      ],
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.help_outline_rounded, color: Colors.white),
          onPressed: _showHelpDialog,
          tooltip: 'مساعدة',
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody(bool isNewAccount) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(ModernAppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Account Info Section
          _buildAccountInfoSection(),

          // New Account Notice
          if (isNewAccount) ...[
            const SizedBox(height: ModernAppTheme.spacingM),
            _buildNewAccountNotice(),
          ],

          // Transaction Type Section
          const SizedBox(height: ModernAppTheme.spacingM),
          _buildTransactionTypeSection(),

          // Form Section
          const SizedBox(height: ModernAppTheme.spacingM),
          _buildFormSection(),

          // Save Button
          const SizedBox(height: ModernAppTheme.spacingL),
          _buildSaveButton(),

          // Bottom Spacing
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  /// بناء قسم معلومات الحساب
  Widget _buildAccountInfoSection() {
    return ModernUIComponents.modernCard(
      margin: EdgeInsets.zero,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: ModernAppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.account_balance_wallet_rounded,
              color: ModernAppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: ModernAppTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.account.name,
                  style: ModernAppTheme.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'الرصيد الحالي: ${widget.account.formattedBalanceWithCurrency(_selectedCurrency?.symbol ?? 'SAR')}',
                  style: ModernAppTheme.bodySmall.copyWith(
                    color: ModernAppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إشعار الحساب الجديد
  Widget _buildNewAccountNotice() {
    return Container(
      padding: const EdgeInsets.all(ModernAppTheme.spacingM),
      decoration: BoxDecoration(
        color: ModernAppTheme.infoColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ModernAppTheme.radiusM),
        border: Border.all(
          color: ModernAppTheme.infoColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: ModernAppTheme.infoColor,
            size: 16,
          ),
          const SizedBox(width: ModernAppTheme.spacingS),
          Expanded(
            child: Text(
              'هذه هي المعاملة الافتتاحية للحساب. اختر نوع المعاملة وأدخل المبلغ.',
              style: ModernAppTheme.bodySmall.copyWith(
                color: ModernAppTheme.infoColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم نوع المعاملة
  Widget _buildTransactionTypeSection() {
    return ModernUIComponents.modernCard(
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نوع المعاملة',
            style: ModernAppTheme.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: ModernAppTheme.spacingM),
          Row(
            children: [
              Expanded(
                child: _buildTransactionTypeCard(
                  title: 'إيداع',
                  subtitle: 'إضافة مبلغ',
                  icon: Icons.add_circle_rounded,
                  color: ModernAppTheme.successColor,
                  isSelected: _selectedType == TransactionType.credit,
                  onTap: () => setState(() => _selectedType = TransactionType.credit),
                ),
              ),
              const SizedBox(width: ModernAppTheme.spacingS),
              Expanded(
                child: _buildTransactionTypeCard(
                  title: 'سحب',
                  subtitle: 'خصم مبلغ',
                  icon: Icons.remove_circle_rounded,
                  color: ModernAppTheme.errorColor,
                  isSelected: _selectedType == TransactionType.debit,
                  onTap: () => setState(() => _selectedType = TransactionType.debit),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء كارت نوع المعاملة
  Widget _buildTransactionTypeCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(ModernAppTheme.spacingS),
        decoration: BoxDecoration(
          color: isSelected
              ? color.withValues(alpha: 0.1)
              : ModernAppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(ModernAppTheme.radiusM),
          border: Border.all(
            color: isSelected
                ? color
                : ModernAppTheme.textTertiary.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 18,
              ),
            ),
            const SizedBox(height: ModernAppTheme.spacingS),
            Text(
              title,
              style: ModernAppTheme.bodyMedium.copyWith(
                color: isSelected ? color : ModernAppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: ModernAppTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم النموذج
  Widget _buildFormSection() {
    return ModernUIComponents.modernCard(
      margin: EdgeInsets.zero,
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل المعاملة',
              style: ModernAppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: ModernAppTheme.spacingM),

            // التاريخ والعملة
            Row(
              children: [
                Expanded(
                  child: _buildDateField(),
                ),
                const SizedBox(width: ModernAppTheme.spacingS),
                Expanded(
                  child: _buildCurrencyField(),
                ),
              ],
            ),

            const SizedBox(height: ModernAppTheme.spacingM),

            // المبلغ
            ModernUIComponents.modernTextField(
              label: 'المبلغ *',
              hint: 'أدخل المبلغ',
              prefixIcon: Icons.attach_money_rounded,
              controller: _amountController,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المبلغ';
                }
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                if (double.parse(value) <= 0) {
                  return 'يجب أن يكون المبلغ أكبر من صفر';
                }
                return null;
              },
            ),

            const SizedBox(height: ModernAppTheme.spacingM),

            // الوصف
            ModernUIComponents.modernTextField(
              label: 'وصف المعاملة (اختياري)',
              hint: 'أدخل وصف المعاملة',
              prefixIcon: Icons.description_rounded,
              controller: _descriptionController,
              maxLines: 2,
            ),

            const SizedBox(height: ModernAppTheme.spacingM),

            // المرفقات
            _buildAttachmentSection(),
          ],
        ),
      ),
    );
  }

  /// بناء حقل التاريخ
  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التاريخ',
          style: ModernAppTheme.labelLarge,
        ),
        const SizedBox(height: ModernAppTheme.spacingS),
        GestureDetector(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ModernAppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
              border: Border.all(
                color: ModernAppTheme.textTertiary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today_rounded,
                  color: ModernAppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(_selectedDate),
                    style: ModernAppTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء حقل العملة
  Widget _buildCurrencyField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العملة',
          style: ModernAppTheme.labelLarge,
        ),
        const SizedBox(height: ModernAppTheme.spacingS),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: ModernAppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
            border: Border.all(
              color: ModernAppTheme.textTertiary.withValues(alpha: 0.3),
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Currency>(
              value: _availableCurrencies.contains(_selectedCurrency)
                  ? _selectedCurrency
                  : (_availableCurrencies.isNotEmpty ? _availableCurrencies.first : null),
              isExpanded: true,
              hint: Text(
                'اختر العملة',
                style: ModernAppTheme.bodyMedium,
              ),
              items: _availableCurrencies.map((currency) {
                return DropdownMenuItem<Currency>(
                  value: currency,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: ModernAppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          currency.symbol,
                          style: TextStyle(
                            color: ModernAppTheme.primaryColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          currency.name,
                          style: ModernAppTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (Currency? newValue) {
                setState(() {
                  _selectedCurrency = newValue;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قسم المرفقات
  Widget _buildAttachmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المرفقات (اختياري)',
          style: ModernAppTheme.labelLarge,
        ),
        const SizedBox(height: ModernAppTheme.spacingS),
        AttachmentPicker(
          onAttachmentChanged: (attachment) {
            setState(() {
              _selectedAttachment = attachment;
            });
          },
          initialAttachment: _selectedAttachment,
        ),
      ],
    );
  }

  /// بناء زر الحفظ
  Widget _buildSaveButton() {
    return Container(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      child: Row(
        children: [
          Expanded(
            child: ModernUIComponents.modernButton(
              text: 'إلغاء',
              onPressed: () => Navigator.pop(context),
              isOutlined: true,
              textColor: ModernAppTheme.textSecondary,
              borderColor: ModernAppTheme.textTertiary,
            ),
          ),
          const SizedBox(width: ModernAppTheme.spacingM),
          Expanded(
            flex: 2,
            child: ModernUIComponents.modernButton(
              text: 'حفظ المعاملة',
              onPressed: _isLoading ? null : _saveTransaction,
              isLoading: _isLoading,
              icon: Icons.save_rounded,
              gradientColors: const [
                ModernAppTheme.primaryColor,
                ModernAppTheme.primaryLight,
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة'),
        content: const Text('أدخل تفاصيل المعاملة الجديدة. اختر نوع المعاملة والمبلغ والوصف.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}