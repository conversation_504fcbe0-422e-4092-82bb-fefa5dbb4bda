import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../models/transaction.dart' as app_trans;
import '../models/category.dart' as cat;
import '../models/user.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'accounts.db');
    // لا نحذف قاعدة البيانات - نحافظ على البيانات الموجودة
    return await openDatabase(
      path,
      version: 9,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // إضافة الحقول الجديدة للحسابات إذا لم تكن موجودة
      try {
        await db.execute('ALTER TABLE accounts ADD COLUMN phoneNumber TEXT');
      } catch (e) {
        // الحقل موجود بالفعل
      }
      try {
        await db.execute('ALTER TABLE accounts ADD COLUMN address TEXT');
      } catch (e) {
        // الحقل موجود بالفعل
      }
      try {
        await db.execute('ALTER TABLE accounts ADD COLUMN notes TEXT');
      } catch (e) {
        // الحقل موجود بالفعل
      }
    }

    if (oldVersion < 3) {
      // إضافة أي تحديثات جديدة للإصدار 3
      try {
        await db.execute(
          'ALTER TABLE accounts ADD COLUMN initialBalance REAL DEFAULT 0',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
      try {
        await db.execute(
          'ALTER TABLE accounts ADD COLUMN initialIsPositive INTEGER DEFAULT 1',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
    }

    if (oldVersion < 4) {
      // إضافة حقول العملة للمعاملات
      try {
        await db.execute(
          'ALTER TABLE transactions ADD COLUMN currencyCode TEXT',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
      try {
        await db.execute(
          'ALTER TABLE transactions ADD COLUMN currencySymbol TEXT',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
    }

    if (oldVersion < 5) {
      // إضافة حقول المرفقات للمعاملات
      try {
        await db.execute(
          'ALTER TABLE transactions ADD COLUMN attachmentPath TEXT',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
      try {
        await db.execute(
          'ALTER TABLE transactions ADD COLUMN attachmentType TEXT',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
      try {
        await db.execute(
          'ALTER TABLE transactions ADD COLUMN attachmentName TEXT',
        );
      } catch (e) {
        // الحقل موجود بالفعل
      }
    }

    if (oldVersion < 6) {
      // إنشاء جدول المستخدمين
      try {
        await db.execute('''
          CREATE TABLE users(
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            fullName TEXT NOT NULL,
            passwordHash TEXT NOT NULL,
            email TEXT,
            biometricEnabled INTEGER DEFAULT 0,
            createdAt INTEGER NOT NULL,
            lastLoginAt INTEGER
          )
        ''');
      } catch (e) {
        // الجدول موجود بالفعل
      }
    }

    if (oldVersion < 7) {
      // إنشاء جدول العملات
      try {
        await db.execute('''
          CREATE TABLE currencies(
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            code TEXT NOT NULL UNIQUE,
            isDefault INTEGER NOT NULL DEFAULT 0,
            createdAt INTEGER NOT NULL
          )
        ''');
      } catch (e) {
        // الجدول موجود بالفعل
      }

      // إضافة حقل العملة للحسابات
      try {
        await db.execute('ALTER TABLE accounts ADD COLUMN currencyId TEXT');
      } catch (e) {
        // الحقل موجود بالفعل
      }
    }

    if (oldVersion < 8) {
      // إضافة جدول معلومات الشركة
      try {
        await db.execute('''
          CREATE TABLE company_info(
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            registrationNumber TEXT NOT NULL,
            logoPath TEXT,
            address TEXT NOT NULL,
            phoneNumber TEXT,
            email TEXT,
            createdAt INTEGER NOT NULL,
            updatedAt INTEGER NOT NULL
          )
        ''');
      } catch (e) {
        // الجدول موجود بالفعل
      }
    }

    if (oldVersion < 9) {
      // إضافة أعمدة التصنيفات للحسابات
      try {
        await db.execute('ALTER TABLE accounts ADD COLUMN categoryId TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }
      try {
        await db.execute('ALTER TABLE accounts ADD COLUMN categoryName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إنشاء جدول التصنيفات
      try {
        await db.execute('''
          CREATE TABLE categories(
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT NOT NULL,
            iconCodePoint INTEGER NOT NULL,
            iconFontFamily TEXT,
            colorValue INTEGER NOT NULL,
            createdAt INTEGER NOT NULL,
            updatedAt INTEGER
          )
        ''');
      } catch (e) {
        // الجدول موجود بالفعل
      }

      // إضافة التصنيفات الافتراضية
      await _insertDefaultCategories(db);
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create accounts table
    await db.execute('''
      CREATE TABLE accounts(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phoneNumber TEXT,
        address TEXT,
        notes TEXT,
        balance REAL NOT NULL,
        initialBalance REAL NOT NULL,
        initialIsPositive INTEGER NOT NULL,
        transactionCount INTEGER NOT NULL,
        isPositive INTEGER NOT NULL,
        createdAt INTEGER NOT NULL,
        currencyId TEXT,
        categoryId TEXT,
        categoryName TEXT
      )
    ''');

    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions(
        id TEXT PRIMARY KEY,
        accountId TEXT NOT NULL,
        description TEXT NOT NULL,
        amount REAL NOT NULL,
        date INTEGER NOT NULL,
        type INTEGER NOT NULL,
        notes TEXT,
        currencyCode TEXT,
        currencySymbol TEXT,
        attachmentPath TEXT,
        attachmentType TEXT,
        attachmentName TEXT,
        FOREIGN KEY (accountId) REFERENCES accounts (id) ON DELETE CASCADE
      )
    ''');

    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        fullName TEXT NOT NULL,
        passwordHash TEXT NOT NULL,
        email TEXT,
        biometricEnabled INTEGER DEFAULT 0,
        createdAt INTEGER NOT NULL,
        lastLoginAt INTEGER
      )
    ''');

    // Create currencies table
    await db.execute('''
      CREATE TABLE currencies(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        isDefault INTEGER NOT NULL DEFAULT 0,
        createdAt INTEGER NOT NULL
      )
    ''');

    // Create company info table
    await db.execute('''
      CREATE TABLE company_info(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        registrationNumber TEXT NOT NULL,
        logoPath TEXT,
        address TEXT NOT NULL,
        phoneNumber TEXT,
        email TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        iconCodePoint INTEGER NOT NULL,
        iconFontFamily TEXT,
        colorValue INTEGER NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER
      )
    ''');

    // Only insert sample data if this is the first time creating the database
    // This ensures data is not recreated on every app launch
    await _insertSampleDataIfEmpty(db);

    // إضافة التصنيفات الافتراضية
    await _insertDefaultCategories(db);
  }

  Future<void> _insertSampleDataIfEmpty(Database db) async {
    // التحقق من وجود بيانات في قاعدة البيانات
    final accountsCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM accounts'),
        ) ??
        0;

    // إذا كانت قاعدة البيانات فارغة، لا نضيف أي بيانات تجريبية
    // المستخدم يضيف بياناته بنفسه فقط
    if (accountsCount == 0) {
      debugPrint('قاعدة البيانات فارغة - جاهزة لإضافة البيانات الحقيقية');
    } else {
      debugPrint('تم العثور على $accountsCount حساب في قاعدة البيانات');
    }

    return;
  }

  /// إدراج التصنيفات الافتراضية
  Future<void> _insertDefaultCategories(Database db) async {
    // التحقق من وجود تصنيفات
    final categoriesCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM categories'),
        ) ??
        0;

    if (categoriesCount == 0) {
      final defaultCategories = [
        {
          'id': '1',
          'name': 'العملاء',
          'description': 'حسابات العملاء والزبائن',
          'iconCodePoint': 0xe7fd, // Icons.person_rounded
          'iconFontFamily': 'MaterialIcons',
          'colorValue': 0xFF2196F3,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': '2',
          'name': 'الموردين',
          'description': 'حسابات الموردين والمقاولين',
          'iconCodePoint': 0xe57c, // Icons.business_rounded
          'iconFontFamily': 'MaterialIcons',
          'colorValue': 0xFF4CAF50,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': '3',
          'name': 'المقاولين',
          'description': 'حسابات المقاولين والمتعهدين',
          'iconCodePoint': 0xe8f9, // Icons.construction_rounded
          'iconFontFamily': 'MaterialIcons',
          'colorValue': 0xFFFF9800,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': '4',
          'name': 'الموظفين',
          'description': 'حسابات الموظفين والعاملين',
          'iconCodePoint': 0xe7ef, // Icons.people_rounded
          'iconFontFamily': 'MaterialIcons',
          'colorValue': 0xFF9C27B0,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': '5',
          'name': 'البنوك',
          'description': 'الحسابات البنكية والمالية',
          'iconCodePoint': 0xe0af, // Icons.account_balance_rounded
          'iconFontFamily': 'MaterialIcons',
          'colorValue': 0xFF607D8B,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        },
        {
          'id': '6',
          'name': 'أخرى',
          'description': 'حسابات متنوعة أخرى',
          'iconCodePoint': 0xe574, // Icons.category_rounded
          'iconFontFamily': 'MaterialIcons',
          'colorValue': 0xFF795548,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        },
      ];

      for (final category in defaultCategories) {
        await db.insert('categories', category);
      }
    }
  }

  // Account operations
  Future<List<Account>> getAllAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('accounts');
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<Account?> getAccount(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Account.fromMap(maps.first);
    }
    return null;
  }

  Future<void> insertAccount(Account account) async {
    final db = await database;

    // التأكد من صحة البيانات قبل الحفظ
    final correctedAccount = Account(
      id: account.id,
      name: account.name.trim(), // إزالة المسافات الزائدة
      phoneNumber: account.phoneNumber?.trim(),
      address: account.address?.trim(),
      notes: account.notes?.trim(),
      balance: account.balance.abs(), // التأكد من أن الرصيد موجب
      initialBalance: account.initialBalance.abs(), // الرصيد الابتدائي
      initialIsPositive: account.initialIsPositive, // نوع الرصيد الابتدائي
      transactionCount:
          account.transactionCount >= 0 ? account.transactionCount : 0,
      isPositive: account.isPositive,
      createdAt: account.createdAt,
      currencyId: account.currencyId, // معرف العملة
    );

    await db.insert('accounts', correctedAccount.toMap());
  }

  Future<void> updateAccount(Account account) async {
    final db = await database;
    await db.update(
      'accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  Future<void> deleteAccount(String id) async {
    final db = await database;
    // Delete all transactions for this account first
    await db.delete('transactions', where: 'accountId = ?', whereArgs: [id]);
    // Then delete the account
    await db.delete('accounts', where: 'id = ?', whereArgs: [id]);
  }

  // Transaction operations
  Future<List<app_trans.Transaction>> getTransactionsByAccount(
    String accountId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'accountId = ?',
      whereArgs: [accountId],
      orderBy: 'date DESC',
    );
    return List.generate(
      maps.length,
      (i) => app_trans.Transaction.fromMap(maps[i]),
    );
  }

  Future<List<app_trans.Transaction>> getAllTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      orderBy: 'date DESC',
    );
    return List.generate(
      maps.length,
      (i) => app_trans.Transaction.fromMap(maps[i]),
    );
  }

  Future<void> insertTransaction(app_trans.Transaction transaction) async {
    final db = await database;

    // التأكد من أن المبلغ موجب دائماً قبل الحفظ
    final correctedTransaction = app_trans.Transaction(
      id: transaction.id,
      accountId: transaction.accountId,
      description: transaction.description,
      amount: transaction.amount.abs(), // التأكد من أن المبلغ موجب
      date: transaction.date,
      type: transaction.type,
      notes: transaction.notes,
      currencyCode: transaction.currencyCode,
      currencySymbol: transaction.currencySymbol,
      attachmentPath: transaction.attachmentPath,
      attachmentType: transaction.attachmentType,
      attachmentName: transaction.attachmentName,
    );

    await db.insert('transactions', correctedTransaction.toMap());

    // تحديث رصيد الحساب وعدد المعاملات
    await _updateAccountAfterTransaction(correctedTransaction);
  }

  Future<void> updateTransaction(app_trans.Transaction transaction) async {
    final db = await database;

    // التأكد من أن المبلغ موجب دائماً قبل الحفظ
    final correctedTransaction = app_trans.Transaction(
      id: transaction.id,
      accountId: transaction.accountId,
      description: transaction.description,
      amount: transaction.amount.abs(), // التأكد من أن المبلغ موجب
      date: transaction.date,
      type: transaction.type,
      notes: transaction.notes,
      currencyCode: transaction.currencyCode,
      currencySymbol: transaction.currencySymbol,
      attachmentPath: transaction.attachmentPath,
      attachmentType: transaction.attachmentType,
      attachmentName: transaction.attachmentName,
    );

    await db.update(
      'transactions',
      correctedTransaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );

    // تحديث رصيد الحساب بعد التعديل
    await _updateAccountAfterTransaction(correctedTransaction);
  }

  Future<void> deleteTransaction(String id) async {
    final db = await database;

    // Get the transaction before deleting to update account balance
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final transaction = app_trans.Transaction.fromMap(maps.first);

      // Delete the transaction
      await db.delete('transactions', where: 'id = ?', whereArgs: [id]);

      // Update account balance after deletion
      await _updateAccountAfterTransaction(transaction);
    }
  }

  Future<void> _updateAccountAfterTransaction(
    app_trans.Transaction transaction,
  ) async {
    final account = await getAccount(transaction.accountId);
    if (account != null) {
      final transactions = await getTransactionsByAccount(account.id);

      // البدء بالرصيد الابتدائي للحساب
      double currentBalance =
          account.initialIsPositive
              ? account.initialBalance
              : -account.initialBalance;

      // حساب تأثير جميع المعاملات على الرصيد
      for (var t in transactions) {
        // التأكد من أن المبلغ موجب دائماً
        final amount = t.amount.abs();

        if (t.type == app_trans.TransactionType.credit) {
          currentBalance += amount; // إضافة للرصيد (دائن)
        } else if (t.type == app_trans.TransactionType.debit) {
          currentBalance -= amount; // خصم من الرصيد (مدين)
        }
      }

      final updatedAccount = account.copyWith(
        balance: currentBalance.abs(), // القيمة المطلقة للرصيد
        transactionCount: transactions.length, // عدد المعاملات
        isPositive: currentBalance >= 0, // هل الرصيد موجب (دائن) أم سالب (مدين)
      );

      await updateAccount(updatedAccount);
    }
  }

  // Search operations
  Future<List<Account>> searchAccounts(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where:
          'name LIKE ? OR phoneNumber LIKE ? OR address LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<List<app_trans.Transaction>> searchTransactions(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'description LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'date DESC',
    );
    return List.generate(
      maps.length,
      (i) => app_trans.Transaction.fromMap(maps[i]),
    );
  }

  // Reports and Statistics
  Future<Map<String, double>> getCategoryTotals() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('transactions');
    final Map<String, double> categoryTotals = {};

    for (var map in maps) {
      final transaction = app_trans.Transaction.fromMap(map);
      final category = _extractCategory(
        transaction.notes ?? transaction.description,
      );

      if (!categoryTotals.containsKey(category)) {
        categoryTotals[category] = 0;
      }

      categoryTotals[category] = categoryTotals[category]! + transaction.amount;
    }

    return categoryTotals;
  }

  String _extractCategory(String text) {
    final categories = [
      'مبيعات',
      'مشتريات',
      'رواتب',
      'إيجارات',
      'اتصالات',
      'معدات',
      'موردين',
      'عملاء',
      'مصروفات',
      'تحصيلات',
    ];

    for (var category in categories) {
      if (text.contains(category)) {
        return category;
      }
    }

    return 'أخرى';
  }

  Future<List<app_trans.Transaction>> getTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'transactions',
      where: 'date >= ? AND date <= ?',
      whereArgs: [
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      ],
      orderBy: 'date DESC',
    );
    return List.generate(
      maps.length,
      (i) => app_trans.Transaction.fromMap(maps[i]),
    );
  }

  Future<List<Map<String, dynamic>>> getMonthlyReport() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('transactions');
    final Map<String, Map<String, double>> monthlyData = {};

    for (var map in maps) {
      final transaction = app_trans.Transaction.fromMap(map);
      final date = DateTime.fromMillisecondsSinceEpoch(
        transaction.date.millisecondsSinceEpoch,
      );
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';

      if (!monthlyData.containsKey(monthKey)) {
        monthlyData[monthKey] = {'credit': 0, 'debit': 0, 'count': 0};
      }

      if (transaction.type == app_trans.TransactionType.credit) {
        monthlyData[monthKey]!['credit'] =
            monthlyData[monthKey]!['credit']! + transaction.amount;
      } else {
        monthlyData[monthKey]!['debit'] =
            monthlyData[monthKey]!['debit']! + transaction.amount;
      }
      monthlyData[monthKey]!['count'] = monthlyData[monthKey]!['count']! + 1;
    }

    final result =
        monthlyData.entries.map((entry) {
          return {
            'month': entry.key,
            'credit': entry.value['credit'],
            'debit': entry.value['debit'],
            'balance': entry.value['credit']! - entry.value['debit']!,
            'count': entry.value['count'],
          };
        }).toList();

    result.sort(
      (a, b) => (b['month'] as String).compareTo(a['month'] as String),
    );
    return result;
  }

  // إعادة حساب جميع الأرصدة لضمان الدقة
  Future<void> recalculateAllBalances() async {
    final accounts = await getAllAccounts();

    for (var account in accounts) {
      final transactions = await getTransactionsByAccount(account.id);

      // البدء بالرصيد الابتدائي للحساب
      double currentBalance =
          account.initialIsPositive
              ? account.initialBalance
              : -account.initialBalance;

      // حساب تأثير جميع المعاملات على الرصيد
      for (var transaction in transactions) {
        // التأكد من أن المبلغ موجب دائماً
        final amount = transaction.amount.abs();

        if (transaction.type == app_trans.TransactionType.credit) {
          currentBalance += amount; // إضافة للرصيد (دائن)
        } else if (transaction.type == app_trans.TransactionType.debit) {
          currentBalance -= amount; // خصم من الرصيد (مدين)
        }
      }

      final updatedAccount = account.copyWith(
        balance: currentBalance.abs(), // القيمة المطلقة للرصيد
        transactionCount: transactions.length, // عدد المعاملات
        isPositive: currentBalance >= 0, // هل الرصيد موجب (دائن) أم سالب (مدين)
      );

      await updateAccount(updatedAccount);
    }
  }

  // User operations
  Future<User?> getUserByUsername(String username) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> getUserById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<void> insertUser(User user) async {
    final db = await database;
    await db.insert('users', user.toMap());
  }

  Future<void> updateUser(User user) async {
    final db = await database;
    await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<void> deleteUser(String id) async {
    final db = await database;
    await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  // Clear all data
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('transactions');
    await db.delete('accounts');
    await db.delete('users');
  }

  // التحقق من سلامة قاعدة البيانات
  Future<bool> isDatabaseHealthy() async {
    try {
      final db = await database;

      // التحقق من وجود الجداول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table'",
      );

      final tableNames = tables.map((table) => table['name']).toList();
      final requiredTables = ['accounts', 'transactions'];

      for (final tableName in requiredTables) {
        if (!tableNames.contains(tableName)) {
          debugPrint('الجدول المطلوب غير موجود: $tableName');
          return false;
        }
      }

      // التحقق من إمكانية القراءة والكتابة
      final accountsCount =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM accounts'),
          ) ??
          0;

      final transactionsCount =
          Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM transactions'),
          ) ??
          0;

      debugPrint(
        'قاعدة البيانات سليمة: $accountsCount حساب، $transactionsCount معاملة',
      );
      return true;
    } catch (e) {
      debugPrint('خطأ في فحص سلامة قاعدة البيانات: $e');
      return false;
    }
  }

  // إصلاح قاعدة البيانات إذا كانت تالفة
  Future<void> repairDatabase() async {
    try {
      debugPrint('بدء إصلاح قاعدة البيانات...');

      // إغلاق الاتصال الحالي
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      // إعادة إنشاء قاعدة البيانات
      _database = await _initDatabase();

      debugPrint('تم إصلاح قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('فشل في إصلاح قاعدة البيانات: $e');
      rethrow;
    }
  }

  // Category operations
  Future<List<cat.Category>> getAllCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      orderBy: 'createdAt ASC',
    );

    return List.generate(maps.length, (i) {
      return cat.Category.fromMap(maps[i]);
    });
  }

  Future<cat.Category?> getCategoryById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return cat.Category.fromMap(maps.first);
    }
    return null;
  }

  Future<void> insertCategory(cat.Category category) async {
    final db = await database;
    await db.insert(
      'categories',
      category.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> updateCategory(cat.Category category) async {
    final db = await database;
    await db.update(
      'categories',
      category.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<void> deleteCategory(String id) async {
    final db = await database;

    // تحديث الحسابات المرتبطة بالتصنيف
    await db.update(
      'accounts',
      {'categoryId': null, 'categoryName': null},
      where: 'categoryId = ?',
      whereArgs: [id],
    );

    // حذف التصنيف
    await db.delete('categories', where: 'id = ?', whereArgs: [id]);
  }

  Future<List<Account>> getAccountsByCategory(String categoryId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'categoryId = ?',
      whereArgs: [categoryId],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Account.fromMap(maps[i]);
    });
  }
}
