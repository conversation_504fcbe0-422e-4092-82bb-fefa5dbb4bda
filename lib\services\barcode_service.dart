import 'package:barcode/barcode.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import '../models/account.dart';
import '../models/transaction.dart';

class BarcodeService {
  /// إنشاء باركود لحساب معين
  static pw.Widget createAccountBarcode(Account account) {
    try {
      // إنشاء البيانات للباركود
      final barcodeData = _createAccountBarcodeData(account);

      // إنشاء الباركود
      final barcode = Barcode.qrCode();
      final svg = barcode.toSvg(barcodeData, width: 80, height: 80);

      return pw.Container(width: 80, height: 80, child: pw.SvgImage(svg: svg));
    } catch (e) {
      // في حالة فشل إنشاء الباركود، نعرض نص بديل
      return pw.Container(
        width: 80,
        height: 80,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Center(
          child: pw.Text(
            'QR',
            style: pw.TextStyle(
              fontSize: 12,
              color: PdfColors.grey600,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      );
    }
  }

  /// إنشاء باركود صغير للمعاملة
  static pw.Widget createTransactionBarcode(
    Transaction transaction,
    Account account,
  ) {
    try {
      // إنشاء البيانات للباركود
      final barcodeData = _createTransactionBarcodeData(transaction, account);

      // إنشاء الباركود
      final barcode = Barcode.qrCode();
      final svg = barcode.toSvg(barcodeData, width: 40, height: 40);

      return pw.Container(width: 40, height: 40, child: pw.SvgImage(svg: svg));
    } catch (e) {
      // في حالة فشل إنشاء الباركود، نعرض نص بديل
      return pw.Container(
        width: 40,
        height: 40,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(4),
        ),
        child: pw.Center(
          child: pw.Text(
            'QR',
            style: pw.TextStyle(
              fontSize: 8,
              color: PdfColors.grey600,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      );
    }
  }

  /// إنشاء بيانات الباركود للحساب
  static String _createAccountBarcodeData(Account account) {
    final data = {
      'type': 'account',
      'id': account.id,
      'name': account.name,
      'phone': account.phoneNumber ?? '',
      'address': account.address ?? '',
      'balance': account.balance.toStringAsFixed(2),
      'created':
          account.createdAt?.millisecondsSinceEpoch.toString() ??
          DateTime.now().millisecondsSinceEpoch.toString(),
    };

    // تحويل البيانات إلى نص مقروء
    return data.entries
        .where((entry) => entry.value.isNotEmpty)
        .map((entry) => '${entry.key}:${entry.value}')
        .join('|');
  }

  /// إنشاء بيانات الباركود للمعاملة
  static String _createTransactionBarcodeData(
    Transaction transaction,
    Account account,
  ) {
    final data = {
      'type': 'transaction',
      'id': transaction.id,
      'account': account.name,
      'amount': transaction.amount.toStringAsFixed(2),
      'type_ar': transaction.type == TransactionType.credit ? 'دائن' : 'مدين',
      'date': transaction.date.millisecondsSinceEpoch.toString(),
      'desc': transaction.description,
    };

    // تحويل البيانات إلى نص مقروء
    return data.entries
        .where((entry) => entry.value.isNotEmpty)
        .map((entry) => '${entry.key}:${entry.value}')
        .join('|');
  }

  /// إنشاء باركود للتقرير العام
  static pw.Widget createReportBarcode(
    String reportTitle,
    Map<String, dynamic> summary,
  ) {
    try {
      // إنشاء البيانات للباركود
      final barcodeData = _createReportBarcodeData(reportTitle, summary);

      // إنشاء الباركود
      final barcode = Barcode.qrCode();
      final svg = barcode.toSvg(barcodeData, width: 60, height: 60);

      return pw.Container(width: 60, height: 60, child: pw.SvgImage(svg: svg));
    } catch (e) {
      // في حالة فشل إنشاء الباركود، نعرض نص بديل
      return pw.Container(
        width: 60,
        height: 60,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(6),
        ),
        child: pw.Center(
          child: pw.Text(
            'QR',
            style: pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      );
    }
  }

  /// إنشاء بيانات الباركود للتقرير
  static String _createReportBarcodeData(
    String reportTitle,
    Map<String, dynamic> summary,
  ) {
    final data = {
      'type': 'report',
      'title': reportTitle,
      'generated': DateTime.now().millisecondsSinceEpoch.toString(),
      'accounts': summary['totalAccounts']?.toString() ?? '0',
      'transactions': summary['totalTransactions']?.toString() ?? '0',
      'balance': summary['totalBalance']?.toStringAsFixed(2) ?? '0.00',
    };

    // تحويل البيانات إلى نص مقروء
    return data.entries
        .where((entry) => entry.value.isNotEmpty)
        .map((entry) => '${entry.key}:${entry.value}')
        .join('|');
  }

  /// إنشاء باركود بسيط للنص
  static pw.Widget createSimpleBarcode(String text, {double size = 50}) {
    try {
      final barcode = Barcode.qrCode();
      final svg = barcode.toSvg(text, width: size, height: size);

      return pw.Container(
        width: size,
        height: size,
        child: pw.SvgImage(svg: svg),
      );
    } catch (e) {
      return pw.Container(
        width: size,
        height: size,
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(4),
        ),
        child: pw.Center(
          child: pw.Text(
            'QR',
            style: pw.TextStyle(
              fontSize: size * 0.2,
              color: PdfColors.grey600,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      );
    }
  }
}
