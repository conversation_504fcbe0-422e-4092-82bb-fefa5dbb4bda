class AppSettings {
  final bool notificationsEnabled;
  final bool darkModeEnabled;
  final bool autoBackupEnabled;
  final String selectedCurrency;
  final String selectedLanguage;
  final String numberFormat;
  final String backupTime;
  final bool biometricEnabled;
  final bool passwordEnabled;
  final String passwordHash;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String dateFormat;
  final String timeFormat;
  final bool autoSyncEnabled;
  final int backupFrequency; // in days
  final bool showBalanceOnHome;
  final bool confirmBeforeDelete;
  final String defaultTransactionType;
  final double fontSize;
  final String appTheme;

  const AppSettings({
    this.notificationsEnabled = true,
    this.darkModeEnabled = false,
    this.autoBackupEnabled = false,
    this.selectedCurrency = 'ريال سعودي',
    this.selectedLanguage = 'العربية',
    this.numberFormat = '1,234.56',
    this.backupTime = '12:00',
    this.biometricEnabled = false,
    this.passwordEnabled = false,
    this.passwordHash = '',
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.dateFormat = 'dd/MM/yyyy',
    this.timeFormat = '24h',
    this.autoSyncEnabled = false,
    this.backupFrequency = 7,
    this.showBalanceOnHome = true,
    this.confirmBeforeDelete = true,
    this.defaultTransactionType = 'credit',
    this.fontSize = 16.0,
    this.appTheme = 'فاتح',
  });

  AppSettings copyWith({
    bool? notificationsEnabled,
    bool? darkModeEnabled,
    bool? autoBackupEnabled,
    String? selectedCurrency,
    String? selectedLanguage,
    String? numberFormat,
    String? backupTime,
    bool? biometricEnabled,
    bool? passwordEnabled,
    String? passwordHash,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? dateFormat,
    String? timeFormat,
    bool? autoSyncEnabled,
    int? backupFrequency,
    bool? showBalanceOnHome,
    bool? confirmBeforeDelete,
    String? defaultTransactionType,
    double? fontSize,
    String? appTheme,
  }) {
    return AppSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      autoBackupEnabled: autoBackupEnabled ?? this.autoBackupEnabled,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      selectedLanguage: selectedLanguage ?? this.selectedLanguage,
      numberFormat: numberFormat ?? this.numberFormat,
      backupTime: backupTime ?? this.backupTime,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      passwordEnabled: passwordEnabled ?? this.passwordEnabled,
      passwordHash: passwordHash ?? this.passwordHash,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      autoSyncEnabled: autoSyncEnabled ?? this.autoSyncEnabled,
      backupFrequency: backupFrequency ?? this.backupFrequency,
      showBalanceOnHome: showBalanceOnHome ?? this.showBalanceOnHome,
      confirmBeforeDelete: confirmBeforeDelete ?? this.confirmBeforeDelete,
      defaultTransactionType:
          defaultTransactionType ?? this.defaultTransactionType,
      fontSize: fontSize ?? this.fontSize,
      appTheme: appTheme ?? this.appTheme,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'darkModeEnabled': darkModeEnabled,
      'autoBackupEnabled': autoBackupEnabled,
      'selectedCurrency': selectedCurrency,
      'selectedLanguage': selectedLanguage,
      'numberFormat': numberFormat,
      'backupTime': backupTime,
      'biometricEnabled': biometricEnabled,
      'passwordEnabled': passwordEnabled,
      'passwordHash': passwordHash,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'dateFormat': dateFormat,
      'timeFormat': timeFormat,
      'autoSyncEnabled': autoSyncEnabled,
      'backupFrequency': backupFrequency,
      'showBalanceOnHome': showBalanceOnHome,
      'confirmBeforeDelete': confirmBeforeDelete,
      'defaultTransactionType': defaultTransactionType,
      'fontSize': fontSize,
      'appTheme': appTheme,
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      notificationsEnabled: map['notificationsEnabled'] ?? true,
      darkModeEnabled: map['darkModeEnabled'] ?? false,
      autoBackupEnabled: map['autoBackupEnabled'] ?? false,
      selectedCurrency: map['selectedCurrency'] ?? 'ريال سعودي',
      selectedLanguage: map['selectedLanguage'] ?? 'العربية',
      numberFormat: map['numberFormat'] ?? '1,234.56',
      backupTime: map['backupTime'] ?? '12:00',
      biometricEnabled: map['biometricEnabled'] ?? false,
      passwordEnabled: map['passwordEnabled'] ?? false,
      passwordHash: map['passwordHash'] ?? '',
      soundEnabled: map['soundEnabled'] ?? true,
      vibrationEnabled: map['vibrationEnabled'] ?? true,
      dateFormat: map['dateFormat'] ?? 'dd/MM/yyyy',
      timeFormat: map['timeFormat'] ?? '24h',
      autoSyncEnabled: map['autoSyncEnabled'] ?? false,
      backupFrequency: map['backupFrequency'] ?? 7,
      showBalanceOnHome: map['showBalanceOnHome'] ?? true,
      confirmBeforeDelete: map['confirmBeforeDelete'] ?? true,
      defaultTransactionType: map['defaultTransactionType'] ?? 'credit',
      fontSize: map['fontSize']?.toDouble() ?? 16.0,
      appTheme: map['appTheme'] ?? 'فاتح',
    );
  }
}
