import 'dart:io';
import 'dart:typed_data';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;
import '../database/database_helper.dart';
import '../models/company_info.dart';

class CompanyInfoService {
  static const String _tableName = 'company_info';
  static const String _logoDirectory = 'company_logos';

  // Get database instance
  Future<Database> get _database async {
    return await DatabaseHelper().database;
  }

  // Initialize company info table
  Future<void> initializeCompanyInfoTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $_tableName (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        registrationNumber TEXT NOT NULL,
        logoPath TEXT,
        address TEXT NOT NULL,
        phoneNumber TEXT,
        email TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');
  }

  // Get company info (there should be only one)
  Future<CompanyInfo?> getCompanyInfo() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        _tableName,
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return CompanyInfo.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      print('Error getting company info: $e');
      return null;
    }
  }

  // Save or update company info
  Future<bool> saveCompanyInfo(CompanyInfo companyInfo) async {
    try {
      final db = await _database;
      
      // Check if company info already exists
      final existing = await getCompanyInfo();
      
      if (existing != null) {
        // Update existing
        final updatedInfo = companyInfo.copyWith(
          id: existing.id,
          createdAt: existing.createdAt,
          updatedAt: DateTime.now(),
        );
        
        await db.update(
          _tableName,
          updatedInfo.toMap(),
          where: 'id = ?',
          whereArgs: [existing.id],
        );
      } else {
        // Insert new
        final newInfo = companyInfo.copyWith(
          id: CompanyInfo.generateId(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await db.insert(_tableName, newInfo.toMap());
      }
      
      return true;
    } catch (e) {
      print('Error saving company info: $e');
      return false;
    }
  }

  // Save company logo and return the path
  Future<String?> saveCompanyLogo(File imageFile) async {
    try {
      // Get app documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final logoDir = Directory(path.join(appDir.path, _logoDirectory));
      
      // Create directory if it doesn't exist
      if (!await logoDir.exists()) {
        await logoDir.create(recursive: true);
      }

      // Read and compress image
      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        print('Error: Could not decode image');
        return null;
      }

      // Resize image to max 512x512 while maintaining aspect ratio
      final resizedImage = img.copyResize(
        image,
        width: image.width > image.height ? 512 : null,
        height: image.height > image.width ? 512 : null,
      );

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'company_logo_$timestamp.png';
      final logoPath = path.join(logoDir.path, fileName);

      // Save compressed image
      final compressedBytes = img.encodePng(resizedImage);
      final logoFile = File(logoPath);
      await logoFile.writeAsBytes(compressedBytes);

      return logoPath;
    } catch (e) {
      print('Error saving company logo: $e');
      return null;
    }
  }

  // Delete old logo file
  Future<void> deleteOldLogo(String? logoPath) async {
    if (logoPath == null || logoPath.isEmpty) return;
    
    try {
      final file = File(logoPath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print('Error deleting old logo: $e');
    }
  }

  // Get logo file
  Future<File?> getLogoFile() async {
    try {
      final companyInfo = await getCompanyInfo();
      if (companyInfo?.logoPath == null) return null;
      
      final file = File(companyInfo!.logoPath!);
      if (await file.exists()) {
        return file;
      }
      return null;
    } catch (e) {
      print('Error getting logo file: $e');
      return null;
    }
  }

  // Get logo bytes for PDF reports
  Future<Uint8List?> getLogoBytes() async {
    try {
      final logoFile = await getLogoFile();
      if (logoFile == null) return null;
      
      return await logoFile.readAsBytes();
    } catch (e) {
      print('Error getting logo bytes: $e');
      return null;
    }
  }

  // Check if company info exists
  Future<bool> hasCompanyInfo() async {
    final info = await getCompanyInfo();
    return info != null;
  }

  // Delete company info
  Future<bool> deleteCompanyInfo() async {
    try {
      final db = await _database;
      final companyInfo = await getCompanyInfo();
      
      if (companyInfo != null) {
        // Delete logo file
        await deleteOldLogo(companyInfo.logoPath);
        
        // Delete from database
        await db.delete(
          _tableName,
          where: 'id = ?',
          whereArgs: [companyInfo.id],
        );
      }
      
      return true;
    } catch (e) {
      print('Error deleting company info: $e');
      return false;
    }
  }

  // Get company info for reports (with fallback)
  Future<CompanyInfo> getCompanyInfoForReports() async {
    final info = await getCompanyInfo();
    return info ?? CompanyInfo.getDefault();
  }

  // Validate company info
  Map<String, String?> validateCompanyInfo({
    required String name,
    required String registrationNumber,
    required String address,
    String? phoneNumber,
    String? email,
  }) {
    return {
      'name': CompanyInfo.validateName(name),
      'registrationNumber': CompanyInfo.validateRegistrationNumber(registrationNumber),
      'address': CompanyInfo.validateAddress(address),
      'phoneNumber': CompanyInfo.validatePhoneNumber(phoneNumber),
      'email': CompanyInfo.validateEmail(email),
    };
  }

  // Clean up old logo files (maintenance)
  Future<void> cleanupOldLogos() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final logoDir = Directory(path.join(appDir.path, _logoDirectory));
      
      if (!await logoDir.exists()) return;
      
      final companyInfo = await getCompanyInfo();
      final currentLogoPath = companyInfo?.logoPath;
      
      final files = await logoDir.list().toList();
      for (final file in files) {
        if (file is File && file.path != currentLogoPath) {
          // Delete old logo files
          await file.delete();
        }
      }
    } catch (e) {
      print('Error cleaning up old logos: $e');
    }
  }
}
