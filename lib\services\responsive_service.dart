import 'package:flutter/material.dart';

enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

enum ScreenSize {
  small,
  medium,
  large,
  extraLarge,
}

class ResponsiveService {
  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  static const double largeDesktopBreakpoint = 1600;

  // Get device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else if (width < largeDesktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  // Get screen size category
  static ScreenSize getScreenSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return ScreenSize.small;
    } else if (width < tabletBreakpoint) {
      return ScreenSize.medium;
    } else if (width < largeDesktopBreakpoint) {
      return ScreenSize.large;
    } else {
      return ScreenSize.extraLarge;
    }
  }

  // Check if mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  // Check if tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  // Check if desktop
  static bool isDesktop(BuildContext context) {
    final type = getDeviceType(context);
    return type == DeviceType.desktop || type == DeviceType.largeDesktop;
  }

  // Get responsive padding
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(24);
      case DeviceType.desktop:
        return const EdgeInsets.all(32);
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(40);
    }
  }

  // Get responsive margin
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(8);
      case DeviceType.tablet:
        return const EdgeInsets.all(12);
      case DeviceType.desktop:
        return const EdgeInsets.all(16);
      case DeviceType.largeDesktop:
        return const EdgeInsets.all(20);
    }
  }

  // Get responsive font size
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseFontSize;
      case DeviceType.tablet:
        return baseFontSize * 1.1;
      case DeviceType.desktop:
        return baseFontSize * 1.2;
      case DeviceType.largeDesktop:
        return baseFontSize * 1.3;
    }
  }

  // Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseIconSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseIconSize;
      case DeviceType.tablet:
        return baseIconSize * 1.2;
      case DeviceType.desktop:
        return baseIconSize * 1.4;
      case DeviceType.largeDesktop:
        return baseIconSize * 1.6;
    }
  }

  // Get responsive card width
  static double getResponsiveCardWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return width - 32; // Full width with padding
      case DeviceType.tablet:
        return width * 0.8; // 80% of screen width
      case DeviceType.desktop:
        return width * 0.6; // 60% of screen width
      case DeviceType.largeDesktop:
        return width * 0.5; // 50% of screen width
    }
  }

  // Get responsive grid columns
  static int getResponsiveGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 1;
      case DeviceType.tablet:
        return 2;
      case DeviceType.desktop:
        return 3;
      case DeviceType.largeDesktop:
        return 4;
    }
  }

  // Get responsive app bar height
  static double getResponsiveAppBarHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight;
      case DeviceType.tablet:
        return kToolbarHeight + 8;
      case DeviceType.desktop:
        return kToolbarHeight + 16;
      case DeviceType.largeDesktop:
        return kToolbarHeight + 24;
    }
  }

  // Get responsive button height
  static double getResponsiveButtonHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 48;
      case DeviceType.tablet:
        return 52;
      case DeviceType.desktop:
        return 56;
      case DeviceType.largeDesktop:
        return 60;
    }
  }

  // Get responsive spacing
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSpacing;
      case DeviceType.tablet:
        return baseSpacing * 1.2;
      case DeviceType.desktop:
        return baseSpacing * 1.4;
      case DeviceType.largeDesktop:
        return baseSpacing * 1.6;
    }
  }

  // Get responsive border radius
  static double getResponsiveBorderRadius(BuildContext context, double baseBorderRadius) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseBorderRadius;
      case DeviceType.tablet:
        return baseBorderRadius * 1.1;
      case DeviceType.desktop:
        return baseBorderRadius * 1.2;
      case DeviceType.largeDesktop:
        return baseBorderRadius * 1.3;
    }
  }

  // Get responsive elevation
  static double getResponsiveElevation(BuildContext context, double baseElevation) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseElevation;
      case DeviceType.tablet:
        return baseElevation * 1.2;
      case DeviceType.desktop:
        return baseElevation * 1.4;
      case DeviceType.largeDesktop:
        return baseElevation * 1.6;
    }
  }

  // Get responsive container constraints
  static BoxConstraints getResponsiveConstraints(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return BoxConstraints(
          maxWidth: width,
          minWidth: width,
        );
      case DeviceType.tablet:
        return BoxConstraints(
          maxWidth: width * 0.9,
          minWidth: 600,
        );
      case DeviceType.desktop:
        return BoxConstraints(
          maxWidth: 1200,
          minWidth: 900,
        );
      case DeviceType.largeDesktop:
        return BoxConstraints(
          maxWidth: 1600,
          minWidth: 1200,
        );
    }
  }

  // Get responsive layout type
  static bool shouldUseDrawer(BuildContext context) {
    return isMobile(context) || isTablet(context);
  }

  // Get responsive navigation type
  static bool shouldUseBottomNavigation(BuildContext context) {
    return isMobile(context);
  }

  // Get responsive sidebar width
  static double getResponsiveSidebarWidth(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return MediaQuery.of(context).size.width * 0.8;
      case DeviceType.tablet:
        return 300;
      case DeviceType.desktop:
        return 320;
      case DeviceType.largeDesktop:
        return 350;
    }
  }
}
