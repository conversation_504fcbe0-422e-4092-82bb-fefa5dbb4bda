import 'package:pdf/widgets.dart' as pw;

class FontFallbackService {
  static final FontFallbackService _instance = FontFallbackService._internal();
  factory FontFallbackService() => _instance;
  FontFallbackService._internal();

  // Cache for downloaded fonts
  final Map<String, pw.Font> _fontCache = {};
  bool _initialized = false;

  // Initialize fallback fonts (simplified version)
  Future<void> initializeFallbackFonts() async {
    if (_initialized) return;

    try {
      // Try to load any available system fonts for fallback
      await _loadSystemFonts();
      _initialized = true;
    } catch (e) {
      // Continue without fallback fonts if loading fails
      _initialized = true;
    }
  }

  // Load system fonts as fallback
  Future<void> _loadSystemFonts() async {
    try {
      // Try to load basic system fonts that might be available
      final systemFonts = ['Arial', 'Helvetica', 'Times', 'Courier'];

      for (final fontName in systemFonts) {
        try {
          // This is a placeholder - in real implementation,
          // we would load system fonts if available
          _fontCache[fontName] = pw.Font.helvetica();
          break; // Use first available font
        } catch (e) {
          continue;
        }
      }
    } catch (e) {
      // No system fonts available
    }
  }

  // Get fallback fonts for comprehensive character support
  List<pw.Font> getFallbackFonts() {
    final fonts = <pw.Font>[];

    // Add cached fonts
    for (final font in _fontCache.values) {
      fonts.add(font);
    }

    return fonts;
  }

  // Get specific font by name
  pw.Font? getFont(String name) {
    return _fontCache[name];
  }

  // Check if fonts are initialized
  bool get isInitialized => _initialized;

  // Get fonts status
  Map<String, bool> getFontsStatus() {
    return {
      'initialized': _initialized,
      'roboto': _fontCache.containsKey('Roboto-Regular'),
      'symbols': _fontCache.containsKey('NotoSansSymbols'),
      'emoji': _fontCache.containsKey('NotoEmoji'),
      'totalFonts': _fontCache.isNotEmpty,
    };
  }

  // Clear font cache
  void clearCache() {
    _fontCache.clear();
    _initialized = false;
  }
}
