import 'package:flutter/material.dart';
import 'package:country_code_picker/country_code_picker.dart';
import '../models/account.dart';
import '../database/database_helper.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart' as modern;
import '../theme/app_theme.dart';

class EditAccountScreen extends StatefulWidget {
  final Account account;

  const EditAccountScreen({super.key, required this.account});

  @override
  State<EditAccountScreen> createState() => _EditAccountScreenState();
}

class _EditAccountScreenState extends State<EditAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  final DatabaseHelper _storage = DatabaseHelper();

  bool _isPositive = true;
  bool _isLoading = false;
  String _countryCode = '+966'; // Default to Saudi Arabia

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.account.name;
    _addressController.text = widget.account.address ?? '';
    _notesController.text = widget.account.notes ?? '';
    _isPositive = widget.account.isPositive;

    // Extract country code and phone number
    if (widget.account.phoneNumber != null &&
        widget.account.phoneNumber!.isNotEmpty) {
      String fullPhone = widget.account.phoneNumber!;
      if (fullPhone.startsWith('+966')) {
        _countryCode = '+966';
        _phoneController.text = fullPhone.substring(4);
      } else if (fullPhone.startsWith('+971')) {
        _countryCode = '+971';
        _phoneController.text = fullPhone.substring(4);
      } else if (fullPhone.startsWith('+965')) {
        _countryCode = '+965';
        _phoneController.text = fullPhone.substring(4);
      } else if (fullPhone.startsWith('+974')) {
        _countryCode = '+974';
        _phoneController.text = fullPhone.substring(4);
      } else if (fullPhone.startsWith('+')) {
        // Extract country code from other numbers
        int spaceIndex = fullPhone.indexOf(' ');
        if (spaceIndex > 0) {
          _countryCode = fullPhone.substring(0, spaceIndex);
          _phoneController.text = fullPhone.substring(spaceIndex + 1);
        } else {
          // Assume first 4 characters are country code
          _countryCode = fullPhone.substring(0, 4);
          _phoneController.text = fullPhone.substring(4);
        }
      } else {
        _phoneController.text = fullPhone;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _updateAccount() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Combine country code with phone number
        String? fullPhoneNumber;
        if (_phoneController.text.trim().isNotEmpty) {
          String phoneNumber = _phoneController.text.trim();
          // Remove any existing country code or + sign
          phoneNumber = phoneNumber.replaceAll(RegExp(r'^(\+|00)?966'), '');
          phoneNumber = phoneNumber.replaceAll(RegExp(r'^\+'), '');
          // Add the selected country code
          fullPhoneNumber = '$_countryCode$phoneNumber';
        }

        final updatedAccount = Account(
          id: widget.account.id,
          name: _nameController.text.trim(),
          phoneNumber: fullPhoneNumber,
          address:
              _addressController.text.trim().isEmpty
                  ? null
                  : _addressController.text.trim(),
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
          balance: widget.account.balance,
          initialBalance: widget.account.initialBalance,
          initialIsPositive: widget.account.initialIsPositive,
          transactionCount: widget.account.transactionCount,
          isPositive: _isPositive,
          createdAt: widget.account.createdAt,
        );

        await _storage.updateAccount(updatedAccount);

        if (mounted) {
          Navigator.pop(context, true);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم تحديث بيانات الحساب بنجاح'),
              backgroundColor: AppTheme.successColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث الحساب: $e'),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppTheme.cardShadow,
          ),
          child: Text(
            'تعديل الحساب',
            style: TextStyle(
              color: Colors.white,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 18),
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        centerTitle: true,
        leading: Container(
          margin: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: ResponsiveHelper.getResponsiveBackgroundGradient(
            context,
            true,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: ResponsiveHelper.getResponsivePadding(context),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // Current Account Info
                ModernCard(
                  gradient:
                      widget.account.isPositive
                          ? AppTheme.successGradient
                          : AppTheme.errorGradient,
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          widget.account.isPositive
                              ? Icons.trending_up_rounded
                              : Icons.trending_down_rounded,
                          color: Colors.white,
                          size: ResponsiveHelper.getResponsiveIconSize(
                            context,
                            24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الحساب الحالي',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      12,
                                    ),
                              ),
                            ),
                            Text(
                              widget.account.name,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      18,
                                    ),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${widget.account.formattedBalance} ر.س',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      14,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Edit Form
                ModernCard(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'بيانات الحساب الجديدة',
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              18,
                            ),
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Account Name Field
                        Text(
                          'اسم الحساب',
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              14,
                            ),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _nameController,
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              16,
                            ),
                          ),
                          decoration: InputDecoration(
                            hintText: 'أدخل اسم الحساب الجديد',
                            hintStyle: TextStyle(
                              color: AppTheme.textSecondary,
                              fontSize: ResponsiveHelper.getResponsiveFontSize(
                                context,
                                14,
                              ),
                            ),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppTheme.primaryGradient,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.person_rounded,
                                color: Colors.white,
                                size: ResponsiveHelper.getResponsiveIconSize(
                                  context,
                                  20,
                                ),
                              ),
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppTheme.textLight),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppTheme.primaryColor,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: AppTheme.backgroundColor,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال اسم الحساب';
                            }
                            if (value.trim().length < 2) {
                              return 'يجب أن يكون اسم الحساب أكثر من حرفين';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Phone Number Field with Country Code
                        _buildPhoneField(),

                        const SizedBox(height: 16),

                        // Address Field
                        _buildTextField(
                          controller: _addressController,
                          label: 'العنوان',
                          hint: 'أدخل العنوان (اختياري)',
                          icon: Icons.location_on_rounded,
                          maxLines: 2,
                        ),

                        const SizedBox(height: 16),

                        // Notes Field
                        _buildTextField(
                          controller: _notesController,
                          label: 'ملاحظات',
                          hint: 'أدخل ملاحظات إضافية (اختياري)',
                          icon: Icons.note_rounded,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 24),

                        // Account Type Selection
                        Text(
                          'نوع الحساب',
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              14,
                            ),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 12),

                        Row(
                          children: [
                            Expanded(
                              child: _buildTypeOption(
                                title: 'دائن (لنا)',
                                subtitle: 'مبلغ مستحق لنا',
                                icon: Icons.trending_up_rounded,
                                isSelected: _isPositive,
                                gradient: AppTheme.successGradient,
                                onTap: () => setState(() => _isPositive = true),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTypeOption(
                                title: 'مدين (علينا)',
                                subtitle: 'مبلغ مستحق علينا',
                                icon: Icons.trending_down_rounded,
                                isSelected: !_isPositive,
                                gradient: AppTheme.errorGradient,
                                onTap:
                                    () => setState(() => _isPositive = false),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // Update Button
                        SizedBox(
                          width: double.infinity,
                          child: modern.ModernButton(
                            text: 'تحديث الحساب',
                            onPressed: _isLoading ? null : _updateAccount,
                            isLoading: _isLoading,
                            gradient: AppTheme.primaryGradient,
                            icon: Icons.update_rounded,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: isSelected ? gradient : null,
          color: isSelected ? null : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.transparent : AppTheme.textLight,
            width: 2,
          ),
          boxShadow: isSelected ? AppTheme.cardShadow : null,
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Colors.white.withValues(alpha: 0.2)
                        : AppTheme.textLight.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                size: ResponsiveHelper.getResponsiveIconSize(context, 20),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textPrimary,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color:
                    isSelected
                        ? Colors.white.withValues(alpha: 0.8)
                        : AppTheme.textSecondary,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 10),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int? maxLines,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines ?? 1,
          textAlign: TextAlign.right,
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: AppTheme.textSecondary,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            ),
            prefixIcon: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: ResponsiveHelper.getResponsiveIconSize(context, 20),
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.textLight),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
            ),
            filled: true,
            fillColor: AppTheme.backgroundColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validator,
        ),
      ],
    );
  }

  // Build phone field with country code picker
  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رقم الهاتف',
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.textLight),
          ),
          child: Row(
            children: [
              // Country Code Picker
              Container(
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: CountryCodePicker(
                  onChanged: (country) {
                    setState(() {
                      _countryCode = country.dialCode!;
                    });
                  },
                  initialSelection:
                      _countryCode == '+966'
                          ? 'SA'
                          : _countryCode == '+971'
                          ? 'AE'
                          : _countryCode == '+965'
                          ? 'KW'
                          : _countryCode == '+974'
                          ? 'QA'
                          : 'SA',
                  favorite: const [
                    '+966',
                    'SA',
                    '+971',
                    'AE',
                    '+965',
                    'KW',
                    '+974',
                    'QA',
                  ],
                  showCountryOnly: false,
                  showOnlyCountryWhenClosed: false,
                  alignLeft: false,
                  textStyle: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Cairo',
                    fontSize: 14,
                  ),
                  dialogTextStyle: const TextStyle(fontFamily: 'Cairo'),
                  searchStyle: const TextStyle(fontFamily: 'Cairo'),
                  dialogBackgroundColor: Colors.white,
                  barrierColor: Colors.black54,
                  backgroundColor: Colors.transparent,
                  boxDecoration: const BoxDecoration(),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 12,
                  ),
                ),
              ),

              // Phone Number Input
              Expanded(
                child: TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.ltr,
                  style: TextStyle(
                    color: AppTheme.textPrimary,
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      16,
                    ),
                  ),
                  decoration: InputDecoration(
                    hintText: '501234567',
                    hintStyle: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: ResponsiveHelper.getResponsiveFontSize(
                        context,
                        14,
                      ),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                  ),
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      // Remove any non-digit characters for validation
                      String cleanNumber = value.replaceAll(
                        RegExp(r'[^\d]'),
                        '',
                      );
                      if (cleanNumber.length < 8) {
                        return 'رقم الهاتف قصير جداً';
                      }
                      if (cleanNumber.length > 15) {
                        return 'رقم الهاتف طويل جداً';
                      }
                    }
                    return null;
                  },
                ),
              ),

              // Phone Icon
              Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.infoGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.phone_rounded,
                  color: Colors.white,
                  size: ResponsiveHelper.getResponsiveIconSize(context, 20),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'سيتم استخدام رقم الهاتف لإرسال ملخص الحساب عبر WhatsApp أو الرسائل النصية',
          style: TextStyle(
            color: AppTheme.textSecondary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }
}
