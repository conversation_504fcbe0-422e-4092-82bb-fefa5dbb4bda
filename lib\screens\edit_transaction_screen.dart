import 'package:flutter/material.dart';
import '../models/transaction.dart';
import '../models/account.dart';
import '../database/database_helper.dart';
import '../services/custom_currency_service.dart';
import '../models/currency.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart' as modern;
import '../theme/app_theme.dart';

class EditTransactionScreen extends StatefulWidget {
  final Transaction transaction;
  final Account account;

  const EditTransactionScreen({
    super.key,
    required this.transaction,
    required this.account,
  });

  @override
  State<EditTransactionScreen> createState() => _EditTransactionScreenState();
}

class _EditTransactionScreenState extends State<EditTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final DatabaseHelper _storage = DatabaseHelper();
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  TransactionType _selectedType = TransactionType.credit;
  DateTime _selectedDate = DateTime.now();
  Currency? _transactionCurrency;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _descriptionController.text = widget.transaction.description;
    _amountController.text = widget.transaction.amount.toString();
    _notesController.text = widget.transaction.notes ?? '';
    _selectedType = widget.transaction.type;
    _selectedDate = widget.transaction.date;
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _updateTransaction() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final updatedTransaction = Transaction(
          id: widget.transaction.id,
          accountId: widget.transaction.accountId,
          description: _descriptionController.text.trim(),
          amount: double.parse(_amountController.text),
          date: _selectedDate,
          type: _selectedType,
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
          currencyCode: widget.transaction.currencyCode,
          currencySymbol: widget.transaction.currencySymbol,
        );

        await _storage.updateTransaction(updatedTransaction);

        if (mounted) {
          Navigator.pop(context, true);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم تحديث المعاملة بنجاح'),
              backgroundColor: AppTheme.successColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث المعاملة: $e'),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppTheme.cardShadow,
          ),
          child: Text(
            'تعديل المعاملة',
            style: TextStyle(
              color: Colors.white,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 18),
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ),
        centerTitle: true,
        leading: Container(
          margin: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: ResponsiveHelper.getResponsiveBackgroundGradient(
            context,
            true,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: ResponsiveHelper.getResponsivePadding(context),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // Current Transaction Info
                ModernCard(
                  gradient:
                      widget.transaction.type.isPositive
                          ? AppTheme.successGradient
                          : AppTheme.errorGradient,
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          widget.transaction.type.isPositive
                              ? Icons.trending_up_rounded
                              : Icons.trending_down_rounded,
                          color: Colors.white,
                          size: ResponsiveHelper.getResponsiveIconSize(
                            context,
                            24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'المعاملة الحالية',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      12,
                                    ),
                              ),
                            ),
                            Text(
                              widget.transaction.description,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      16,
                                    ),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              widget.transaction.formattedAmount,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                      context,
                                      14,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Edit Form
                ModernCard(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'بيانات المعاملة الجديدة',
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              18,
                            ),
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Description Field
                        _buildTextField(
                          controller: _descriptionController,
                          label: 'وصف المعاملة',
                          hint: 'أدخل وصف المعاملة',
                          icon: Icons.description_rounded,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال وصف المعاملة';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Amount Field
                        _buildTextField(
                          controller: _amountController,
                          label:
                              'المبلغ (${widget.transaction.currencySymbol})',
                          hint: 'أدخل المبلغ بالعملة المحددة',
                          icon: Icons.attach_money_rounded,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال المبلغ';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال مبلغ صحيح';
                            }
                            if (double.parse(value) <= 0) {
                              return 'يجب أن يكون المبلغ أكبر من صفر';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Notes Field
                        _buildTextField(
                          controller: _notesController,
                          label: 'ملاحظات (اختياري)',
                          hint: 'أدخل ملاحظات إضافية',
                          icon: Icons.note_rounded,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 24),

                        // Transaction Type Selection
                        Text(
                          'نوع المعاملة',
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              14,
                            ),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 12),

                        Row(
                          children: [
                            Expanded(
                              child: _buildTypeOption(
                                title: 'دائن',
                                subtitle: 'مبلغ لنا',
                                icon: Icons.trending_up_rounded,
                                isSelected:
                                    _selectedType == TransactionType.credit,
                                gradient: AppTheme.successGradient,
                                onTap:
                                    () => setState(
                                      () =>
                                          _selectedType =
                                              TransactionType.credit,
                                    ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTypeOption(
                                title: 'مدين',
                                subtitle: 'مبلغ علينا',
                                icon: Icons.trending_down_rounded,
                                isSelected:
                                    _selectedType == TransactionType.debit,
                                gradient: AppTheme.errorGradient,
                                onTap:
                                    () => setState(
                                      () =>
                                          _selectedType = TransactionType.debit,
                                    ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Date Selection
                        Text(
                          'تاريخ المعاملة',
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              14,
                            ),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: _selectDate,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundColor,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: AppTheme.textLight),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  margin: const EdgeInsets.only(left: 8),
                                  decoration: BoxDecoration(
                                    gradient: AppTheme.primaryGradient,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.calendar_today_rounded,
                                    color: Colors.white,
                                    size:
                                        ResponsiveHelper.getResponsiveIconSize(
                                          context,
                                          20,
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                  style: TextStyle(
                                    color: AppTheme.textPrimary,
                                    fontSize:
                                        ResponsiveHelper.getResponsiveFontSize(
                                          context,
                                          16,
                                        ),
                                  ),
                                ),
                                const Spacer(),
                                Icon(
                                  Icons.arrow_drop_down_rounded,
                                  color: AppTheme.textSecondary,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 32),

                        // Update Button
                        SizedBox(
                          width: double.infinity,
                          child: modern.ModernButton(
                            text: 'تحديث المعاملة',
                            onPressed: _isLoading ? null : _updateTransaction,
                            isLoading: _isLoading,
                            gradient: AppTheme.primaryGradient,
                            icon: Icons.update_rounded,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          textAlign: TextAlign.right,
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: AppTheme.textSecondary,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
            ),
            prefixIcon: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: ResponsiveHelper.getResponsiveIconSize(context, 20),
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.textLight),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
            ),
            filled: true,
            fillColor: AppTheme.backgroundColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildTypeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: isSelected ? gradient : null,
          color: isSelected ? null : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.transparent : AppTheme.textLight,
            width: 2,
          ),
          boxShadow: isSelected ? AppTheme.cardShadow : null,
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Colors.white.withValues(alpha: 0.2)
                        : AppTheme.textLight.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                size: ResponsiveHelper.getResponsiveIconSize(context, 20),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textPrimary,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color:
                    isSelected
                        ? Colors.white.withValues(alpha: 0.8)
                        : AppTheme.textSecondary,
                fontSize: ResponsiveHelper.getResponsiveFontSize(context, 10),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
