import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/category.dart';
import '../models/account.dart';
import '../services/category_service.dart';
import 'account_details_screen.dart';
import 'add_account_screen.dart';

/// صفحة التصنيفات المتقدمة مع نظام التنقل
class AdvancedCategoriesScreen extends StatefulWidget {
  final String? initialCategoryId;

  const AdvancedCategoriesScreen({super.key, this.initialCategoryId});

  @override
  State<AdvancedCategoriesScreen> createState() =>
      _AdvancedCategoriesScreenState();
}

class _AdvancedCategoriesScreenState extends State<AdvancedCategoriesScreen>
    with TickerProviderStateMixin {
  final CategoryService _categoryService = CategoryService();

  List<Category> _categories = [];
  Map<String, List<Account>> _accountsByCategory = {};
  bool _isLoading = true;

  late PageController _pageController;
  late TabController _tabController;
  int _currentIndex = 0;

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _showAllCategories = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // تحميل التصنيفات
      final categories = await _categoryService.getAllCategories();
      if (categories.isEmpty) {
        await _categoryService.insertDefaultCategories();
        final updatedCategories = await _categoryService.getAllCategories();
        setState(() => _categories = updatedCategories);
      } else {
        setState(() => _categories = categories);
      }

      // إعداد التحكم في الصفحات
      _pageController = PageController(initialPage: 0);
      _tabController = TabController(length: _categories.length, vsync: this);

      // تحديد الصفحة الأولية
      await _setInitialPage();

      // تحميل الحسابات لكل تصنيف
      await _loadAccountsForAllCategories();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorMessage('خطأ في تحميل البيانات: $e');
    }
  }

  /// تحديد الصفحة الأولية
  Future<void> _setInitialPage() async {
    int initialIndex = 0;

    if (widget.initialCategoryId != null) {
      // البحث عن التصنيف المحدد
      initialIndex = _categories.indexWhere(
        (cat) => cat.id == widget.initialCategoryId,
      );
      if (initialIndex == -1) initialIndex = 0;
    } else {
      // استعادة آخر تصنيف تم عرضه
      final prefs = await SharedPreferences.getInstance();
      final lastCategoryId = prefs.getString('last_viewed_category');
      if (lastCategoryId != null) {
        final index = _categories.indexWhere((cat) => cat.id == lastCategoryId);
        if (index != -1) initialIndex = index;
      }
    }

    setState(() => _currentIndex = initialIndex);
    _pageController = PageController(initialPage: initialIndex);
    _tabController = TabController(
      length: _categories.length,
      vsync: this,
      initialIndex: initialIndex,
    );
  }

  /// تحميل الحسابات لجميع التصنيفات
  Future<void> _loadAccountsForAllCategories() async {
    final accountsByCategory = <String, List<Account>>{};

    for (final category in _categories) {
      final accounts = await _categoryService.getAccountsByCategory(
        category.id,
      );
      accountsByCategory[category.id] = accounts;
    }

    setState(() => _accountsByCategory = accountsByCategory);
  }

  /// حفظ آخر تصنيف تم عرضه
  Future<void> _saveLastViewedCategory(String categoryId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_viewed_category', categoryId);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: const Color(0xFFFAFAFA),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Color(0xFF4A5FBF)),
              SizedBox(height: 16),
              Text(
                'جاري تحميل التصنيفات...',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Cairo',
                  color: Color(0xFF6B7280),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFAB(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    final currentCategory =
        _categories.isNotEmpty ? _categories[_currentIndex] : null;

    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors:
                currentCategory != null
                    ? [
                      currentCategory.color,
                      currentCategory.color.withValues(alpha: 0.8),
                    ]
                    : [const Color(0xFF4A5FBF), const Color(0xFF6B73FF)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: (currentCategory?.color ?? const Color(0xFF4A5FBF))
                  .withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (currentCategory != null) ...[
              Icon(currentCategory.icon, color: Colors.white, size: 20),
              const SizedBox(width: 8),
            ],
            Flexible(
              child: Text(
                _showAllCategories
                    ? 'جميع التصنيفات'
                    : currentCategory?.name ?? 'التصنيفات',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Cairo',
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
      centerTitle: true,
      leading: Container(
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFD32F2F), Color(0xFFE53935)],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFD32F2F).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          icon: const Icon(
            Icons.arrow_back_rounded,
            color: Colors.white,
            size: 22,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF10B981), Color(0xFF059669)],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF10B981).withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              _showAllCategories
                  ? Icons.category_rounded
                  : Icons.view_module_rounded,
              color: Colors.white,
              size: 22,
            ),
            onPressed: _toggleViewMode,
            tooltip:
                _showAllCategories
                    ? 'عرض التصنيفات منفصلة'
                    : 'عرض جميع التصنيفات',
          ),
        ),
      ],
      bottom: _showAllCategories ? null : _buildTabBar(),
    );
  }

  /// بناء شريط التبويبات
  PreferredSizeWidget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      indicatorColor: Colors.white,
      indicatorWeight: 3,
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white70,
      labelStyle: const TextStyle(
        fontFamily: 'Cairo',
        fontWeight: FontWeight.bold,
        fontSize: 12,
      ),
      unselectedLabelStyle: const TextStyle(
        fontFamily: 'Cairo',
        fontWeight: FontWeight.normal,
        fontSize: 12,
      ),
      onTap: (index) {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      tabs:
          _categories.map((category) {
            final accountCount = _accountsByCategory[category.id]?.length ?? 0;
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(category.icon, size: 16),
                  const SizedBox(width: 6),
                  Text(category.name),
                  if (accountCount > 0) ...[
                    const SizedBox(width: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        accountCount.toString(),
                        style: const TextStyle(fontSize: 10),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildBody() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),
          // المحتوى
          Expanded(
            child:
                _showAllCategories
                    ? _buildAllCategoriesView()
                    : _buildCategoryPagesView(),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() => _searchQuery = value);
        },
        decoration: InputDecoration(
          hintText:
              _showAllCategories
                  ? 'البحث في جميع التصنيفات...'
                  : 'البحث في ${_categories.isNotEmpty ? _categories[_currentIndex].name : 'التصنيف'}...',
          hintStyle: const TextStyle(
            color: Color(0xFF9CA3AF),
            fontFamily: 'Cairo',
          ),
          prefixIcon: const Icon(
            Icons.search_rounded,
            color: Color(0xFF4A5FBF),
          ),
          suffixIcon:
              _searchQuery.isNotEmpty
                  ? IconButton(
                    icon: const Icon(
                      Icons.clear_rounded,
                      color: Color(0xFF9CA3AF),
                    ),
                    onPressed: () {
                      _searchController.clear();
                      setState(() => _searchQuery = '');
                    },
                  )
                  : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 16),
        ),
        style: const TextStyle(fontFamily: 'Cairo', fontSize: 14),
      ),
    );
  }

  /// بناء عرض جميع التصنيفات
  Widget _buildAllCategoriesView() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        final accounts = _accountsByCategory[category.id] ?? [];
        final filteredAccounts = _getFilteredAccounts(accounts);

        return _buildCategorySection(category, filteredAccounts);
      },
    );
  }

  /// بناء عرض صفحات التصنيفات
  Widget _buildCategoryPagesView() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() => _currentIndex = index);
        _tabController.animateTo(index);
        if (_categories.isNotEmpty) {
          _saveLastViewedCategory(_categories[index].id);
        }
      },
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        final accounts = _accountsByCategory[category.id] ?? [];
        final filteredAccounts = _getFilteredAccounts(accounts);

        return _buildCategoryPage(category, filteredAccounts);
      },
    );
  }

  /// تصفية الحسابات حسب البحث
  List<Account> _getFilteredAccounts(List<Account> accounts) {
    if (_searchQuery.isEmpty) return accounts;

    return accounts.where((account) {
      return account.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (account.phoneNumber?.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ??
              false) ||
          (account.address?.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ??
              false);
    }).toList();
  }

  /// بناء قسم التصنيف (للعرض الشامل)
  Widget _buildCategorySection(Category category, List<Account> accounts) {
    if (accounts.isEmpty && _searchQuery.isNotEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: category.borderColor),
        boxShadow: [
          BoxShadow(
            color: category.color.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس التصنيف
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: category.colorWithOpacity,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: category.color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(category.icon, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    category.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                      color: category.color,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: category.color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${accounts.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              ],
            ),
          ),
          // قائمة الحسابات
          if (accounts.isEmpty)
            _buildEmptyState(category)
          else
            ...accounts
                .take(3)
                .map((account) => _buildAccountCard(account, category)),
          // رابط عرض المزيد
          if (accounts.length > 3)
            Container(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: TextButton(
                  onPressed: () => _navigateToCategory(category),
                  child: Text(
                    'عرض جميع الحسابات (${accounts.length})',
                    style: TextStyle(
                      color: category.color,
                      fontFamily: 'Cairo',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء صفحة التصنيف
  Widget _buildCategoryPage(Category category, List<Account> accounts) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child:
          accounts.isEmpty
              ? _buildEmptyState(category)
              : ListView.builder(
                itemCount: accounts.length,
                itemBuilder: (context, index) {
                  return _buildAccountCard(accounts[index], category);
                },
              ),
    );
  }

  /// بناء بطاقة الحساب
  Widget _buildAccountCard(Account account, Category category) {
    final isPositive = account.balance >= 0;
    final balanceColor =
        isPositive ? const Color(0xFF10B981) : const Color(0xFFEF4444);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: category.color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: category.color.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToAccountDetails(account),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: category.colorWithOpacity,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(category.icon, color: category.color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    if (account.phoneNumber?.isNotEmpty == true) ...[
                      const SizedBox(height: 2),
                      Text(
                        account.phoneNumber!,
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'Cairo',
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${account.balance.toStringAsFixed(2)} ${account.currencyId ?? 'SAR'}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                      color: balanceColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    account.isPositive ? 'دائن' : 'مدين',
                    style: TextStyle(
                      fontSize: 10,
                      fontFamily: 'Cairo',
                      color: balanceColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(Category category) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: category.colorWithOpacity,
                shape: BoxShape.circle,
              ),
              child: Icon(category.icon, size: 48, color: category.color),
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : 'لا توجد حسابات في ${category.name}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
                color: Color(0xFF374151),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'جرب البحث بكلمات مختلفة'
                  : 'ابدأ بإضافة حساب جديد لهذا التصنيف',
              style: const TextStyle(
                fontSize: 12,
                fontFamily: 'Cairo',
                color: Color(0xFF6B7280),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFAB() {
    final currentCategory =
        _categories.isNotEmpty ? _categories[_currentIndex] : null;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              currentCategory != null
                  ? [
                    currentCategory.color,
                    currentCategory.color.withValues(alpha: 0.8),
                  ]
                  : [const Color(0xFF4A5FBF), const Color(0xFF6B73FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (currentCategory?.color ?? const Color(0xFF4A5FBF))
                .withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _addNewAccount,
        backgroundColor: Colors.transparent,
        elevation: 0,
        icon: const Icon(Icons.add_rounded, color: Colors.white),
        label: const Text(
          'إضافة حساب',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }

  /// تبديل وضع العرض
  void _toggleViewMode() {
    setState(() => _showAllCategories = !_showAllCategories);
  }

  /// التنقل لتفاصيل الحساب
  void _navigateToAccountDetails(Account account) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AccountDetailsScreen(account: account),
      ),
    );

    if (result == true) {
      _loadAccountsForAllCategories();
    }
  }

  /// التنقل لتصنيف محدد
  void _navigateToCategory(Category category) {
    final index = _categories.indexWhere((cat) => cat.id == category.id);
    if (index != -1) {
      setState(() {
        _showAllCategories = false;
        _currentIndex = index;
      });
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _tabController.animateTo(index);
    }
  }

  /// إضافة حساب جديد
  void _addNewAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );

    if (result != null) {
      _loadAccountsForAllCategories();
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: const Color(0xFFEF4444),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
