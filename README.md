# تطبيق دفتر الحسابات

تطبيق Flutter لإدارة الحسابات المالية باللغة العربية مع دعم التخطيط من اليمين إلى اليسار (RTL).

## المميزات

- 📱 واجهة مستخدم عربية جميلة ومنظمة
- 💰 إدارة الحسابات المالية (دائن/مدين)
- ➕ إضافة حسابات جديدة
- 📊 عرض إجمالي المبالغ (لنا/علينا)
- 🎨 تصميم متجاوب يعمل على جميع أحجام الشاشات
- 🔄 دعم التخطيط من اليمين إلى اليسار

## بنية المشروع

```
lib/
├── main.dart                    # نقطة البداية الرئيسية
├── models/
│   └── account.dart            # نموذج بيانات الحساب
├── screens/
│   ├── accounts_screen.dart    # الشاشة الرئيسية لعرض الحسابات
│   └── add_account_screen.dart # شاشة إضافة حساب جديد
└── widgets/
    └── account_item.dart       # عنصر عرض الحساب الواحد
```

## كيفية التشغيل

1. تأكد من تثبيت Flutter على جهازك
2. افتح Terminal في مجلد المشروع
3. قم بتشغيل الأوامر التالية:

```bash
flutter pub get
flutter run
```

## الشاشات

### الشاشة الرئيسية
- عرض قائمة بجميع الحسابات
- إظهار اسم الحساب والرصيد وعدد المعاملات
- مؤشر بصري للحسابات الدائنة (سهم أخضر للأعلى) والمدينة (سهم أحمر للأسفل)
- شريط سفلي يعرض إجمالي المبالغ
- زر إضافة حساب جديد

### شاشة إضافة حساب
- إدخال اسم الحساب
- إدخال الرصيد الابتدائي
- اختيار نوع الحساب (دائن/مدين)
- زر حفظ الحساب

## التصميم

التطبيق يستخدم:
- اللون الأزرق الأساسي: `#4A90E2`
- خلفية رمادية فاتحة: `Colors.grey[100]`
- بطاقات بيضاء مع ظلال خفيفة
- أيقونات Material Design
- خط عربي واضح

## البيانات التجريبية

التطبيق يحتوي على بيانات تجريبية تشمل:
- سعيد محسن (8,000 - مدين)
- عبدالله علي (1,000 - مدين)
- غانم أحمد (200 - دائن)
- أحمد محمد (16,300 - دائن)
- بسام سعيد (14,500 - مدين)
- حسين صالح (10,000 - مدين)
- وليد محمد (10,500 - مدين)

## المميزات المتقدمة الجديدة ✨

### ✅ قاعدة البيانات المحلية (SQLite)
- حفظ البيانات محلياً على الجهاز
- استرجاع سريع للبيانات
- بيانات تجريبية جاهزة

### ✅ تفاصيل المعاملات
- صفحة تفاصيل لكل حساب
- عرض جميع المعاملات مع التواريخ والأوقات
- إضافة معاملات جديدة (دائن/مدين)
- ملاحظات اختيارية لكل معاملة

### ✅ وظيفة البحث المتقدمة
- البحث في الحسابات والمعاملات
- نتائج فورية أثناء الكتابة
- تبويبات منفصلة للحسابات والمعاملات

### ✅ التقارير والإحصائيات
- ملخص عام للحسابات والمعاملات
- رسوم بيانية دائرية للأرصدة
- توزيع الحسابات (دائن/مدين)
- آخر المعاملات

### ✅ وظيفة المشاركة
- مشاركة تفاصيل الحسابات
- تصدير التقارير
- مشاركة عبر التطبيقات المختلفة

## التطوير المستقبلي

- إضافة النسخ الاحتياطي والاستعادة
- تصدير البيانات إلى Excel/PDF
- إضافة التنبيهات والتذكيرات
- دعم العملات المتعددة
- إضافة الفئات للمعاملات

## المتطلبات

- Flutter SDK 3.7.2 أو أحدث
- Dart 3.7.2 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو iOS أو محاكي للتشغيل
