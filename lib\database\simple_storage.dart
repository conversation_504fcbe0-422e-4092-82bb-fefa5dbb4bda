import '../models/account.dart';
import '../models/transaction.dart';

class SimpleStorage {
  static final SimpleStorage _instance = SimpleStorage._internal();
  factory SimpleStorage() => _instance;
  SimpleStorage._internal();

  // In-memory storage for web compatibility
  List<Account> _accounts = [];
  List<Transaction> _transactions = [];
  bool _initialized = false;

  Future<void> init() async {
    if (_initialized) return;

    // Initialize with sample data
    _accounts = [
      Account(
        id: '1',
        name: 'سعيد محسن',
        phoneNumber: '***********',
        address: 'شارع الجمهورية، المنصورة',
        notes: 'عميل مهم - دفع شهري',
        balance: 8000,
        initialBalance: 8000,
        initialIsPositive: false,
        transactionCount: 10,
        isPositive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Account(
        id: '2',
        name: 'عبدالله علي',
        phoneNumber: '***********',
        address: 'شارع النيل، القاهرة',
        notes: 'مورد رئيسي',
        balance: 1000,
        initialBalance: 1000,
        initialIsPositive: false,
        transactionCount: 5,
        isPositive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      Account(
        id: '3',
        name: 'غانم أحمد',
        phoneNumber: '***********',
        address: 'شارع الهرم، الجيزة',
        notes: 'عميل جديد',
        balance: 200,
        initialBalance: 200,
        initialIsPositive: true,
        transactionCount: 3,
        isPositive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Account(
        id: '4',
        name: 'أحمد محمد',
        phoneNumber: '***********',
        address: 'شارع الجامعة، الإسكندرية',
        notes: 'عميل VIP - أولوية عالية',
        balance: 16300,
        initialBalance: 16300,
        initialIsPositive: true,
        transactionCount: 87,
        isPositive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      Account(
        id: '5',
        name: 'بسام سعيد',
        phoneNumber: '***********',
        address: 'شارع المعز، القاهرة الفاطمية',
        notes: 'مورد معدات',
        balance: 14500,
        initialBalance: 14500,
        initialIsPositive: false,
        transactionCount: 6,
        isPositive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
      Account(
        id: '6',
        name: 'حسين صالح',
        phoneNumber: '***********',
        address: 'شارع الثورة، طنطا',
        notes: 'عميل موسمي',
        balance: 10000,
        initialBalance: 10000,
        initialIsPositive: false,
        transactionCount: 6,
        isPositive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      Account(
        id: '7',
        name: 'وليد محمد',
        phoneNumber: '***********',
        address: 'شارع السلام، أسوان',
        notes: 'شريك تجاري',
        balance: 10500,
        initialBalance: 10500,
        initialIsPositive: false,
        transactionCount: 5,
        isPositive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    _transactions = [
      Transaction(
        id: 't1',
        accountId: '1',
        description: 'رصيد ابتدائي',
        amount: 8000.0,
        date: DateTime.now().subtract(const Duration(days: 30)),
        type: TransactionType.debit,
        notes: 'الرصيد الابتدائي للحساب',
      ),
      Transaction(
        id: 't2',
        accountId: '4',
        description: 'دفعة من العميل',
        amount: 5000.0,
        date: DateTime.now().subtract(const Duration(days: 5)),
        type: TransactionType.credit,
        notes: 'دفعة جزئية',
      ),
      Transaction(
        id: 't3',
        accountId: '4',
        description: 'فاتورة رقم 123',
        amount: 11300.0,
        date: DateTime.now().subtract(const Duration(days: 15)),
        type: TransactionType.credit,
        notes: 'فاتورة خدمات',
      ),
      Transaction(
        id: 't4',
        accountId: '2',
        description: 'دفعة نقدية',
        amount: 500.0,
        date: DateTime.now().subtract(const Duration(days: 3)),
        type: TransactionType.debit,
        notes: 'دفعة نقدية',
      ),
      Transaction(
        id: 't5',
        accountId: '3',
        description: 'استلام مبلغ',
        amount: 200.0,
        date: DateTime.now().subtract(const Duration(days: 1)),
        type: TransactionType.credit,
        notes: 'استلام مبلغ من العميل',
      ),
    ];

    _initialized = true;
  }

  // Account operations
  Future<List<Account>> getAllAccounts() async {
    await init();
    return List.from(_accounts);
  }

  Future<Account?> getAccount(String id) async {
    await init();
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> insertAccount(Account account) async {
    await init();
    _accounts.add(account);
  }

  Future<void> updateAccount(Account account) async {
    await init();
    final index = _accounts.indexWhere((a) => a.id == account.id);
    if (index != -1) {
      _accounts[index] = account;
    }
  }

  Future<void> deleteAccount(String id) async {
    await init();
    _accounts.removeWhere((account) => account.id == id);
    _transactions.removeWhere((transaction) => transaction.accountId == id);
  }

  // Transaction operations
  Future<List<Transaction>> getTransactionsByAccount(String accountId) async {
    await init();
    final transactions =
        _transactions
            .where((transaction) => transaction.accountId == accountId)
            .toList();
    transactions.sort((a, b) => b.date.compareTo(a.date));
    return transactions;
  }

  Future<List<Transaction>> getAllTransactions() async {
    await init();
    final transactions = List<Transaction>.from(_transactions);
    transactions.sort((a, b) => b.date.compareTo(a.date));
    return transactions;
  }

  Future<void> insertTransaction(Transaction transaction) async {
    await init();
    _transactions.add(transaction);
    await _updateAccountAfterTransaction(transaction);
  }

  Future<void> updateTransaction(Transaction transaction) async {
    await init();
    final index = _transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      _transactions[index] = transaction;
      await _updateAccountAfterTransaction(transaction);
    }
  }

  Future<void> deleteTransaction(String id) async {
    await init();
    _transactions.removeWhere((transaction) => transaction.id == id);
  }

  Future<void> _updateAccountAfterTransaction(Transaction transaction) async {
    final account = await getAccount(transaction.accountId);
    if (account != null) {
      final transactions = await getTransactionsByAccount(account.id);

      double newBalance = 0;
      for (var t in transactions) {
        if (t.type == TransactionType.credit) {
          newBalance += t.amount;
        } else {
          newBalance -= t.amount;
        }
      }

      final updatedAccount = account.copyWith(
        balance: newBalance.abs(),
        transactionCount: transactions.length,
        isPositive: newBalance >= 0,
      );

      await updateAccount(updatedAccount);
    }
  }

  // Search operations
  Future<List<Account>> searchAccounts(String query) async {
    await init();
    return _accounts
        .where(
          (account) => account.name.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();
  }

  Future<List<Transaction>> searchTransactions(String query) async {
    await init();
    final results =
        _transactions
            .where(
              (transaction) =>
                  transaction.description.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  (transaction.notes?.toLowerCase().contains(
                        query.toLowerCase(),
                      ) ??
                      false),
            )
            .toList();
    return List<Transaction>.from(results);
  }

  // Reports and Statistics
  Future<Map<String, double>> getTotalAmounts() async {
    await init();
    double totalCredit = 0;
    double totalDebit = 0;

    for (var transaction in _transactions) {
      if (transaction.type == TransactionType.credit) {
        totalCredit += transaction.amount;
      } else {
        totalDebit += transaction.amount;
      }
    }

    return {
      'credit': totalCredit,
      'debit': totalDebit,
      'balance': totalCredit - totalDebit,
    };
  }

  Future<Map<String, double>> getMonthlyTotals(int year, int month) async {
    await init();
    double totalCredit = 0;
    double totalDebit = 0;

    final monthlyTransactions =
        _transactions.where((transaction) {
          return transaction.date.year == year &&
              transaction.date.month == month;
        }).toList();

    for (var transaction in monthlyTransactions) {
      if (transaction.type == TransactionType.credit) {
        totalCredit += transaction.amount;
      } else {
        totalDebit += transaction.amount;
      }
    }

    return {
      'credit': totalCredit,
      'debit': totalDebit,
      'balance': totalCredit - totalDebit,
      'count': monthlyTransactions.length.toDouble(),
    };
  }

  Future<List<Map<String, dynamic>>> getMonthlyReport() async {
    await init();
    final Map<String, Map<String, double>> monthlyData = {};

    for (var transaction in _transactions) {
      final monthKey =
          '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';

      if (!monthlyData.containsKey(monthKey)) {
        monthlyData[monthKey] = {'credit': 0, 'debit': 0, 'count': 0};
      }

      if (transaction.type == TransactionType.credit) {
        monthlyData[monthKey]!['credit'] =
            monthlyData[monthKey]!['credit']! + transaction.amount;
      } else {
        monthlyData[monthKey]!['debit'] =
            monthlyData[monthKey]!['debit']! + transaction.amount;
      }
      monthlyData[monthKey]!['count'] = monthlyData[monthKey]!['count']! + 1;
    }

    final result =
        monthlyData.entries.map((entry) {
          return {
            'month': entry.key,
            'credit': entry.value['credit'],
            'debit': entry.value['debit'],
            'balance': entry.value['credit']! - entry.value['debit']!,
            'count': entry.value['count'],
          };
        }).toList();

    result.sort(
      (a, b) => (b['month'] as String).compareTo(a['month'] as String),
    );
    return result;
  }

  Future<Map<String, double>> getCategoryTotals() async {
    await init();
    final Map<String, double> categoryTotals = {};

    for (var transaction in _transactions) {
      final category = _extractCategory(
        transaction.notes ?? transaction.description,
      );

      if (!categoryTotals.containsKey(category)) {
        categoryTotals[category] = 0;
      }

      categoryTotals[category] = categoryTotals[category]! + transaction.amount;
    }

    return categoryTotals;
  }

  String _extractCategory(String text) {
    final categories = [
      'مبيعات',
      'مشتريات',
      'رواتب',
      'إيجارات',
      'اتصالات',
      'معدات',
      'موردين',
      'عملاء',
      'مصروفات',
      'تحصيلات',
    ];

    for (var category in categories) {
      if (text.contains(category)) {
        return category;
      }
    }

    return 'أخرى';
  }

  Future<List<Transaction>> getTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    await init();
    return _transactions.where((transaction) {
      return transaction.date.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          transaction.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  Future<Map<String, dynamic>> getAccountSummary(String accountId) async {
    await init();
    final transactions = await getTransactionsByAccount(accountId);
    final account = await getAccount(accountId);

    if (account == null) return {};

    double totalCredit = 0;
    double totalDebit = 0;

    for (var transaction in transactions) {
      if (transaction.type == TransactionType.credit) {
        totalCredit += transaction.amount;
      } else {
        totalDebit += transaction.amount;
      }
    }

    return {
      'account': account,
      'totalCredit': totalCredit,
      'totalDebit': totalDebit,
      'balance': totalCredit - totalDebit,
      'transactionCount': transactions.length,
      'lastTransaction': transactions.isNotEmpty ? transactions.first : null,
    };
  }

  // Backup and Restore
  Future<Map<String, dynamic>> createBackup() async {
    await init();
    return {
      'version': '1.0',
      'created_at': DateTime.now().toIso8601String(),
      'accounts':
          _accounts
              .map(
                (a) => {
                  'id': a.id,
                  'name': a.name,
                  'balance': a.balance,
                  'created_at':
                      a.createdAt?.toIso8601String() ??
                      DateTime.now().toIso8601String(),
                },
              )
              .toList(),
      'transactions':
          _transactions
              .map(
                (t) => {
                  'id': t.id,
                  'account_id': t.accountId,
                  'description': t.description,
                  'amount': t.amount,
                  'date': t.date.toIso8601String(),
                  'type': t.type.toString(),
                  'notes': t.notes,
                },
              )
              .toList(),
    };
  }

  Future<bool> restoreFromBackup(Map<String, dynamic> backupData) async {
    try {
      _accounts.clear();
      _transactions.clear();

      // Restore accounts
      final accountsData = backupData['accounts'] as List;
      for (var accountData in accountsData) {
        final balance = accountData['balance'].toDouble();
        _accounts.add(
          Account(
            id: accountData['id'],
            name: accountData['name'],
            phoneNumber: accountData['phone_number'],
            address: accountData['address'],
            notes: accountData['notes'],
            balance: balance,
            initialBalance: balance,
            initialIsPositive: balance >= 0,
            createdAt: DateTime.parse(accountData['created_at']),
            transactionCount: 0,
            isPositive: balance >= 0,
          ),
        );
      }

      // Restore transactions
      final transactionsData = backupData['transactions'] as List;
      for (var transactionData in transactionsData) {
        _transactions.add(
          Transaction(
            id: transactionData['id'],
            accountId: transactionData['account_id'],
            description: transactionData['description'],
            amount: transactionData['amount'].toDouble(),
            date: DateTime.parse(transactionData['date']),
            type:
                transactionData['type'] == 'TransactionType.credit'
                    ? TransactionType.credit
                    : TransactionType.debit,
            notes: transactionData['notes'],
          ),
        );
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Clear all data
  Future<void> clearAllData() async {
    _accounts.clear();
    _transactions.clear();
    _initialized = false;
  }
}
