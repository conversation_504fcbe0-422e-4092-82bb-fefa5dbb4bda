import 'package:flutter/services.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';

class ArabicFontService {
  static final ArabicFontService _instance = ArabicFontService._internal();
  factory ArabicFontService() => _instance;
  ArabicFontService._internal();

  // Primary fonts
  pw.Font? _arabicFont;
  pw.Font? _arabicBoldFont;

  // Fallback fonts for symbols and emojis
  pw.Font? _symbolFont;
  pw.Font? _emojiFont;
  pw.Font? _latinFont;

  bool _fontsLoaded = false;

  // Load Arabic fonts for PDF with comprehensive fallback support
  Future<void> loadArabicFonts() async {
    if (_fontsLoaded) return;

    try {
      // Load primary Arabic fonts (only use existing fonts)
      _arabicFont = await _loadEmbeddedFont(
        'assets/fonts/NotoSansArabic-Regular.ttf',
      );
      _arabicBoldFont = await _loadEmbeddedFont(
        'assets/fonts/NotoSansArabic-Bold.ttf',
      );

      // Use Arabic fonts as fallback for Latin and symbols if specific fonts not available
      _latinFont = _arabicFont;
      _symbolFont = _arabicFont;
      _emojiFont = _arabicFont;

      _fontsLoaded = true;
    } catch (e) {
      // Use system fonts as fallback
      _fontsLoaded = true;
    }
  }

  // Load embedded font from assets
  Future<pw.Font?> _loadEmbeddedFont(String assetPath) async {
    try {
      final fontData = await rootBundle.load(assetPath);
      return pw.Font.ttf(fontData);
    } catch (e) {
      return null;
    }
  }

  // Get comprehensive text style for PDF with fallback fonts
  pw.TextStyle getArabicTextStyle({
    double fontSize = 12,
    bool bold = false,
    PdfColor? color,
  }) {
    // Use the appropriate font based on bold setting
    pw.Font? primaryFont;
    if (bold && _arabicBoldFont != null) {
      primaryFont = _arabicBoldFont;
    } else if (_arabicFont != null) {
      primaryFont = _arabicFont;
    }

    // Create fallback fonts list (use same font for consistency)
    final fallbackFonts = <pw.Font>[];
    if (primaryFont != null) {
      fallbackFonts.add(primaryFont);
    }

    // Add the other Arabic font as fallback
    if (bold && _arabicFont != null && _arabicFont != primaryFont) {
      fallbackFonts.add(_arabicFont!);
    } else if (!bold &&
        _arabicBoldFont != null &&
        _arabicBoldFont != primaryFont) {
      fallbackFonts.add(_arabicBoldFont!);
    }

    return pw.TextStyle(
      font: primaryFont,
      fontFallback: fallbackFonts.length > 1 ? fallbackFonts.sublist(1) : [],
      fontSize: fontSize,
      color: color ?? PdfColors.black,
      fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
    );
  }

  // Get Arabic header style with fallback support
  pw.TextStyle getArabicHeaderStyle({double fontSize = 18, PdfColor? color}) {
    return getArabicTextStyle(fontSize: fontSize, bold: true, color: color);
  }

  // Get specific font for symbols and special characters
  pw.TextStyle getSymbolTextStyle({double fontSize = 12, PdfColor? color}) {
    final fallbackFonts = <pw.Font>[];

    // Add symbol font first
    if (_symbolFont != null) {
      fallbackFonts.add(_symbolFont!);
    }

    // Add Latin font as fallback
    if (_latinFont != null) {
      fallbackFonts.add(_latinFont!);
    }

    // Add Arabic font as last resort
    if (_arabicFont != null) {
      fallbackFonts.add(_arabicFont!);
    }

    return pw.TextStyle(
      font: fallbackFonts.isNotEmpty ? fallbackFonts.first : null,
      fontFallback: fallbackFonts.length > 1 ? fallbackFonts.sublist(1) : [],
      fontSize: fontSize,
      color: color ?? PdfColors.black,
    );
  }

  // Check if fonts are loaded
  bool get fontsLoaded => _fontsLoaded;

  // Get regular Arabic font
  pw.Font? get arabicFont => _arabicFont;

  // Get bold Arabic font
  pw.Font? get arabicBoldFont => _arabicBoldFont;

  // Get available fonts info for debugging
  Map<String, bool> getFontsStatus() {
    return {
      'arabicFont': _arabicFont != null,
      'arabicBoldFont': _arabicBoldFont != null,
      'latinFont': _latinFont != null,
      'symbolFont': _symbolFont != null,
      'emojiFont': _emojiFont != null,
      'fontsLoaded': _fontsLoaded,
    };
  }

  // Force reload fonts
  Future<void> reloadFonts() async {
    _fontsLoaded = false;
    _arabicFont = null;
    _arabicBoldFont = null;
    _latinFont = null;
    _symbolFont = null;
    _emojiFont = null;
    await loadArabicFonts();
  }
}
