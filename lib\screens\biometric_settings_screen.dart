import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../theme/app_theme.dart';

class BiometricSettingsScreen extends StatefulWidget {
  const BiometricSettingsScreen({super.key});

  @override
  State<BiometricSettingsScreen> createState() =>
      _BiometricSettingsScreenState();
}

class _BiometricSettingsScreenState extends State<BiometricSettingsScreen> {
  final AuthService _authService = AuthService();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _biometricAvailable = false;
  bool _biometricEnabled = false;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _loadBiometricStatus();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loadBiometricStatus() async {
    final isAvailable = await _authService.isBiometricAvailable();
    final currentUser = _authService.currentUser;

    setState(() {
      _biometricAvailable = isAvailable;
      _biometricEnabled = currentUser?.biometricEnabled ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات البصمة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // معلومات البصمة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // أيقونة البصمة
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color:
                            _biometricAvailable
                                ? AppTheme.primaryColor.withValues(alpha: 0.1)
                                : Colors.grey.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.fingerprint_rounded,
                        size: 48,
                        color:
                            _biometricAvailable
                                ? AppTheme.primaryColor
                                : Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // عنوان الحالة
                    Text(
                      _getBiometricStatusTitle(),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),

                    // وصف الحالة
                    Text(
                      _getBiometricStatusDescription(),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),

                    // مفتاح التفعيل
                    if (_biometricAvailable)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'تفعيل البصمة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Switch(
                            value: _biometricEnabled,
                            onChanged: _isLoading ? null : _toggleBiometric,
                            activeColor: AppTheme.primaryColor,
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // معلومات إضافية
              if (_biometricAvailable) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline_rounded,
                        color: Colors.blue.shade600,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'عند تفعيل البصمة، ستتمكن من تسجيل الدخول باستخدام بصمتك بدلاً من كلمة المرور.',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.shade200, width: 1),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.orange.shade600,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'البصمة غير متاحة على هذا الجهاز أو لم يتم إعدادها في إعدادات النظام.',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const Spacer(),

              // زر العودة
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade100,
                    foregroundColor: Colors.black87,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'العودة',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getBiometricStatusTitle() {
    if (!_biometricAvailable) {
      return 'البصمة غير متاحة';
    }
    return _biometricEnabled ? 'البصمة مفعلة' : 'البصمة غير مفعلة';
  }

  String _getBiometricStatusDescription() {
    if (!_biometricAvailable) {
      return 'هذا الجهاز لا يدعم المصادقة بالبصمة أو لم يتم إعدادها';
    }
    if (_biometricEnabled) {
      return 'يمكنك تسجيل الدخول باستخدام بصمتك';
    }
    return 'قم بتفعيل البصمة لتسجيل دخول أسرع وأكثر أماناً';
  }

  Future<void> _toggleBiometric(bool value) async {
    if (value) {
      // تفعيل البصمة - نحتاج كلمة المرور للتأكيد
      _showPasswordDialog();
    } else {
      // إلغاء تفعيل البصمة
      await _disableBiometric();
    }
  }

  void _showPasswordDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد كلمة المرور'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'يرجى إدخال كلمة المرور لتفعيل البصمة',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور',
                    prefixIcon: const Icon(Icons.lock_rounded),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility_rounded
                            : Icons.visibility_off_rounded,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _passwordController.clear();
                  Navigator.pop(context);
                },
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: _isLoading ? null : _enableBiometric,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Text('تفعيل'),
              ),
            ],
          ),
    );
  }

  Future<void> _enableBiometric() async {
    if (_passwordController.text.isEmpty) {
      _showErrorSnackBar('يرجى إدخال كلمة المرور');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await _authService.enableBiometric(
        _passwordController.text,
      );

      if (result.success) {
        // إعادة تحميل بيانات المستخدم
        await _authService.refreshCurrentUser();

        setState(() {
          _biometricEnabled = true;
        });
        _passwordController.clear();
        if (mounted) {
          Navigator.pop(context);
          _showSuccessSnackBar('تم تفعيل البصمة بنجاح');
        }
      } else {
        if (mounted) {
          _showErrorSnackBar(result.message ?? 'فشل في تفعيل البصمة');
        }
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تفعيل البصمة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _disableBiometric() async {
    setState(() => _isLoading = true);

    try {
      final result = await _authService.disableBiometric();

      if (result.success) {
        // إعادة تحميل بيانات المستخدم
        await _authService.refreshCurrentUser();

        setState(() {
          _biometricEnabled = false;
        });
        if (mounted) {
          _showSuccessSnackBar('تم إلغاء تفعيل البصمة');
        }
      } else {
        if (mounted) {
          _showErrorSnackBar(result.message ?? 'فشل في إلغاء تفعيل البصمة');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في إلغاء تفعيل البصمة: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
