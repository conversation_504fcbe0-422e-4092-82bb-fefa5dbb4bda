import 'package:flutter/material.dart';
import '../theme/modern_app_theme.dart';

/// مكونات UI مخصصة ومبتكرة للتطبيق المحاسبي
class ModernUIComponents {
  
  /// كارت حديث مع تأثيرات بصرية
  static Widget modernCard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    bool elevated = false,
    Color? backgroundColor,
    List<Color>? gradientColors,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.all(ModernAppTheme.spacingM),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(ModernAppTheme.radiusXL),
          child: Container(
            padding: padding ?? const EdgeInsets.all(ModernAppTheme.spacingL),
            decoration: gradientColors != null
                ? BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: gradientColors,
                    ),
                    borderRadius: BorderRadius.circular(ModernAppTheme.radiusXL),
                    boxShadow: elevated 
                        ? ModernAppTheme.elevatedShadow 
                        : ModernAppTheme.cardShadow,
                  )
                : BoxDecoration(
                    color: backgroundColor ?? ModernAppTheme.cardColor,
                    borderRadius: BorderRadius.circular(ModernAppTheme.radiusXL),
                    boxShadow: elevated 
                        ? ModernAppTheme.elevatedShadow 
                        : ModernAppTheme.cardShadow,
                  ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// زر حديث مع تدرج لوني
  static Widget modernButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isPrimary = true,
    bool isExpanded = false,
    bool isOutlined = false,
    List<Color>? gradientColors,
    Color? textColor,
    Color? borderColor,
  }) {
    Widget buttonChild = isLoading
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : ClipRect(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // If width is very constrained, show only icon or text
                if (constraints.maxWidth < 60) {
                  return Center(
                    child: icon != null
                        ? Icon(icon, size: 14)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              text,
                              style: const TextStyle(fontSize: 10),
                              maxLines: 1,
                            ),
                          ),
                  );
                }

                return Center(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (icon != null) ...[
                          Icon(icon, size: 14),
                          const SizedBox(width: 2),
                        ],
                        Text(
                          text,
                          style: const TextStyle(fontSize: 11),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );

    if (isOutlined) {
      return Container(
        width: isExpanded ? double.infinity : null,
        decoration: BoxDecoration(
          border: Border.all(
            color: borderColor ?? ModernAppTheme.primaryColor,
            width: 1.5,
          ),
          borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: ModernAppTheme.spacingL,
                vertical: ModernAppTheme.spacingM,
              ),
              child: DefaultTextStyle(
                style: ModernAppTheme.bodyMedium.copyWith(
                  color: textColor ?? ModernAppTheme.primaryColor,
                ),
                child: IconTheme(
                  data: IconThemeData(
                    color: textColor ?? ModernAppTheme.primaryColor,
                  ),
                  child: buttonChild,
                ),
              ),
            ),
          ),
        ),
      );
    } else if (isPrimary) {
      return Container(
        width: isExpanded ? double.infinity : null,
        decoration: BoxDecoration(
          gradient: gradientColors != null
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: gradientColors,
                )
              : ModernAppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
          boxShadow: [
            BoxShadow(
              color: ModernAppTheme.primaryColor.withValues(alpha: 0.4),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: ModernAppTheme.spacingL,
                vertical: ModernAppTheme.spacingM,
              ),
              child: DefaultTextStyle(
                style: ModernAppTheme.bodyMedium.copyWith(
                  color: Colors.white,
                ),
                child: IconTheme(
                  data: const IconThemeData(
                    color: Colors.white,
                  ),
                  child: buttonChild,
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      return SizedBox(
        width: isExpanded ? double.infinity : null,
        child: OutlinedButton(
          onPressed: onPressed,
          style: ModernAppTheme.secondaryButtonStyle,
          child: buttonChild,
        ),
      );
    }
  }

  /// حقل إدخال حديث
  static Widget modernTextField({
    required String label,
    String? hint,
    IconData? prefixIcon,
    Widget? suffixIcon,
    TextEditingController? controller,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool obscureText = false,
    int maxLines = 1,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ModernAppTheme.labelLarge,
        ),
        const SizedBox(height: ModernAppTheme.spacingS),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          obscureText: obscureText,
          maxLines: maxLines,
          enabled: enabled,
          style: ModernAppTheme.bodyLarge,
          decoration: ModernAppTheme.getInputDecoration(
            label: '',
            hint: hint,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
          ).copyWith(
            labelText: null,
            floatingLabelBehavior: FloatingLabelBehavior.never,
          ),
        ),
      ],
    );
  }

  /// شريط تطبيق حديث
  static PreferredSizeWidget modernAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    Color? backgroundColor,
    List<Color>? gradientColors,
    double elevation = 0,
  }) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: gradientColors != null
            ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: gradientColors,
                ),
              )
            : BoxDecoration(
                color: backgroundColor ?? Colors.transparent,
              ),
        child: AppBar(
          title: Text(
            title,
            style: ModernAppTheme.headingSmall.copyWith(
              color: gradientColors != null 
                  ? ModernAppTheme.textOnPrimary 
                  : ModernAppTheme.textPrimary,
            ),
          ),
          centerTitle: centerTitle,
          backgroundColor: Colors.transparent,
          elevation: elevation,
          leading: leading,
          actions: actions,
          iconTheme: IconThemeData(
            color: gradientColors != null 
                ? ModernAppTheme.textOnPrimary 
                : ModernAppTheme.textPrimary,
          ),
        ),
      ),
    );
  }

  /// مؤشر تحميل حديث
  static Widget modernLoadingIndicator({
    String? message,
    Color? color,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(ModernAppTheme.spacingL),
            decoration: BoxDecoration(
              color: ModernAppTheme.cardColor,
              borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
              boxShadow: ModernAppTheme.cardShadow,
            ),
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? ModernAppTheme.primaryColor,
              ),
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: ModernAppTheme.spacingL),
            Text(
              message,
              style: ModernAppTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// رسالة فارغة حديثة
  static Widget modernEmptyState({
    required IconData icon,
    required String title,
    required String message,
    String? actionText,
    VoidCallback? onAction,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(ModernAppTheme.spacingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(ModernAppTheme.spacingXL),
              decoration: BoxDecoration(
                color: ModernAppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                icon,
                size: 64,
                color: ModernAppTheme.primaryColor.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: ModernAppTheme.spacingL),
            Text(
              title,
              style: ModernAppTheme.headingMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: ModernAppTheme.spacingM),
            Text(
              message,
              style: ModernAppTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: ModernAppTheme.spacingL),
              modernButton(
                text: actionText,
                onPressed: onAction,
                icon: Icons.add_rounded,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// شريط إشعار حديث
  static void showModernSnackBar({
    required BuildContext context,
    required String message,
    bool isError = false,
    bool isSuccess = false,
    Duration duration = const Duration(seconds: 3),
  }) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    if (isError) {
      backgroundColor = ModernAppTheme.errorColor;
      textColor = Colors.white;
      icon = Icons.error_rounded;
    } else if (isSuccess) {
      backgroundColor = ModernAppTheme.successColor;
      textColor = Colors.white;
      icon = Icons.check_circle_rounded;
    } else {
      backgroundColor = ModernAppTheme.primaryColor;
      textColor = Colors.white;
      icon = Icons.info_rounded;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: textColor, size: ModernAppTheme.iconSizeM),
            const SizedBox(width: ModernAppTheme.spacingM),
            Expanded(
              child: Text(
                message,
                style: ModernAppTheme.bodyMedium.copyWith(color: textColor),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernAppTheme.radiusM),
        ),
        duration: duration,
      ),
    );
  }
}
