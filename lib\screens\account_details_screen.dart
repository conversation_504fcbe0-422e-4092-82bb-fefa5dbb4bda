import 'package:flutter/material.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../database/database_helper.dart';

import '../theme/app_theme.dart';
import '../services/custom_currency_service.dart';
import '../models/currency.dart';
import 'add_amount_screen.dart';
import 'edit_transaction_screen.dart';
import 'account_options_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Account account;

  const AccountDetailsScreen({super.key, required this.account});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  final DatabaseHelper _storage = DatabaseHelper();
  final CustomCurrencyService _currencyService = CustomCurrencyService();

  List<Transaction> transactions = [];
  List<Transaction> filteredTransactions = [];
  List<Currency> usedCurrencies = [];
  bool isLoading = true;
  Currency? _accountCurrency;
  Currency? _selectedFilterCurrency;
  Account? currentAccount;

  @override
  void initState() {
    super.initState();
    currentAccount = widget.account;
    _loadAccountCurrency();
    _loadTransactions();
  }

  Future<void> _loadAccountCurrency() async {
    if (widget.account.currencyId != null) {
      final currency = await _currencyService.getCurrencyById(
        widget.account.currencyId!,
      );
      if (mounted) {
        setState(() {
          _accountCurrency = currency;
        });
      }
    } else {
      final defaultCurrency = await _currencyService.getDefaultCurrency();
      if (mounted) {
        setState(() {
          _accountCurrency = defaultCurrency;
        });
      }
    }
  }

  Future<void> _loadTransactions() async {
    setState(() {
      isLoading = true;
    });

    try {
      final loadedTransactions = await _storage.getTransactionsByAccount(
        widget.account.id,
      );
      final updatedAccount = await _storage.getAccount(widget.account.id);

      // Extract unique currencies used in transactions
      final currencyCodesSet = <String>{};
      final currenciesUsed = <Currency>[];

      for (final transaction in loadedTransactions) {
        if (transaction.currencyCode != null &&
            !currencyCodesSet.contains(transaction.currencyCode)) {
          currencyCodesSet.add(transaction.currencyCode!);

          // Try to get currency details
          final currency = await _currencyService.getCurrencyByCode(
            transaction.currencyCode!,
          );
          if (currency != null) {
            currenciesUsed.add(currency);
          }
        }
      }

      // Reset filter if selected currency is not in the used currencies
      if (_selectedFilterCurrency != null &&
          !currenciesUsed.any((c) => c.id == _selectedFilterCurrency!.id)) {
        _selectedFilterCurrency = null;
      }

      if (mounted) {
        setState(() {
          transactions = loadedTransactions;
          usedCurrencies = currenciesUsed;
          currentAccount = updatedAccount;
          isLoading = false;
        });
        _applyFilter();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _applyFilter() {
    if (_selectedFilterCurrency == null) {
      // Show all transactions
      filteredTransactions = List.from(transactions);
    } else {
      // Filter by selected currency - use both code and symbol for better matching
      filteredTransactions =
          transactions
              .where(
                (transaction) =>
                    transaction.currencyCode == _selectedFilterCurrency!.code ||
                    transaction.currencySymbol ==
                        _selectedFilterCurrency!.symbol,
              )
              .toList();
    }
    setState(() {});
  }

  void _onCurrencyFilterChanged(Currency? currency) {
    setState(() {
      // Only set the currency if it's null or exists in the used currencies
      if (currency == null || usedCurrencies.any((c) => c.id == currency.id)) {
        _selectedFilterCurrency = currency;
      }
    });
    _applyFilter();
  }

  // حساب الملخص المالي للمعاملات المفلترة
  Map<String, double> _getFilteredSummary() {
    double totalCredit = 0;
    double totalDebit = 0;

    for (final transaction in filteredTransactions) {
      if (transaction.type == TransactionType.credit) {
        totalCredit += transaction.amount;
      } else {
        totalDebit += transaction.amount;
      }
    }

    return {
      'credit': totalCredit,
      'debit': totalDebit,
      'balance': totalCredit - totalDebit,
    };
  }

  // حساب الملخص المالي لكل عملة على حدة
  Map<String, Map<String, double>> _getCurrencySummaries() {
    final summaries = <String, Map<String, double>>{};

    // حساب الملخص لكل عملة
    for (final currency in usedCurrencies) {
      final currencyTransactions =
          transactions
              .where(
                (t) =>
                    t.currencyCode == currency.code ||
                    t.currencySymbol == currency.symbol,
              )
              .toList();

      double totalCredit = 0;
      double totalDebit = 0;

      for (final transaction in currencyTransactions) {
        if (transaction.type == TransactionType.credit) {
          totalCredit += transaction.amount;
        } else {
          totalDebit += transaction.amount;
        }
      }

      summaries[currency.code] = {
        'credit': totalCredit,
        'debit': totalDebit,
        'balance': totalCredit - totalDebit,
        'transactionCount': currencyTransactions.length.toDouble(),
      };
    }

    // ترتيب العملات حسب الأهمية (الرصيد الأعلى أولاً، ثم عدد المعاملات)
    final sortedEntries =
        summaries.entries.toList()..sort((a, b) {
          final balanceA = a.value['balance']!.abs();
          final balanceB = b.value['balance']!.abs();
          final countA = a.value['transactionCount']!;
          final countB = b.value['transactionCount']!;

          // ترتيب حسب الرصيد أولاً، ثم عدد المعاملات
          final balanceComparison = balanceB.compareTo(balanceA);
          if (balanceComparison != 0) return balanceComparison;
          return countB.compareTo(countA);
        });

    return Map.fromEntries(sortedEntries);
  }

  Future<void> _addTransaction() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddAmountScreen(account: currentAccount!),
      ),
    );

    if (result == true) {
      await _loadTransactions();
    }
  }

  Future<void> _editTransaction(Transaction transaction) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => EditTransactionScreen(
              transaction: transaction,
              account: currentAccount!,
            ),
      ),
    );

    if (result == true) {
      await _loadTransactions();
    }
  }

  Future<void> _deleteTransaction(Transaction transaction) async {
    try {
      await _storage.deleteTransaction(transaction.id);
      await _loadTransactions();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف المعاملة "${transaction.description}" بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المعاملة: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _shareAccountDetails() async {
    if (currentAccount?.phoneNumber == null ||
        currentAccount!.phoneNumber!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('رقم الهاتف غير متوفر لهذا الحساب'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // Get current currency
    final currencySymbol = _accountCurrency?.symbol ?? 'SAR';

    // Create summary message
    final content = StringBuffer();
    content.writeln('كشف حساب: ${currentAccount!.name}');
    content.writeln(
      'الرصيد الحالي: ${currentAccount!.formattedBalanceWithCurrency(currencySymbol)}',
    );

    if (transactions.isNotEmpty) {
      content.writeln('\nآخر المعاملات:');
      final recentTransactions = transactions.take(5);
      for (final transaction in recentTransactions) {
        content.writeln(
          '${transaction.formattedDate}: ${transaction.formattedAmount} - ${transaction.description}',
        );
      }
    }

    try {
      final phoneNumber = currentAccount!.phoneNumber!;
      final message = Uri.encodeComponent(content.toString());
      final whatsappUrl = 'https://wa.me/$phoneNumber?text=$message';

      final uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم فتح واتساب لإرسال كشف الحساب'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } else {
        throw 'لا يمكن فتح واتساب';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح واتساب: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showAccountOptions() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) =>
                AccountOptionsScreen(account: currentAccount!),
        transitionDuration: const Duration(milliseconds: 300),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    ).then((result) {
      if (result == 'updated') {
        _loadTransactions(); // This will reload the account data too
      } else if (result == 'deleted' && mounted) {
        Navigator.pop(context, true); // Go back to previous screen
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (currentAccount == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        slivers: [
          // Custom App Bar with Hero Section
          _buildHeroAppBar(),

          // Account Info Card
          SliverToBoxAdapter(child: _buildAccountInfoCard()),

          // Balance Dashboard
          SliverToBoxAdapter(child: _buildBalanceDashboard()),

          // Currency Filter (if needed)
          if (usedCurrencies.isNotEmpty)
            SliverToBoxAdapter(child: _buildCurrencyFilter()),

          // Quick Actions
          SliverToBoxAdapter(child: _buildQuickActions()),

          // Transactions Header
          SliverToBoxAdapter(child: _buildTransactionsHeader()),

          // Transactions List
          _buildTransactionsList(),
        ],
      ),
      // Floating Action Button for Add Transaction
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  // بناء Hero App Bar المبتكر
  Widget _buildHeroAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: const Color(0xFF4A5FBF),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF), Color(0xFF9C27B0)],
            ),
          ),
          child: Stack(
            children: [
              // Background Pattern
              Positioned.fill(
                child: CustomPaint(painter: HeroPatternPainter()),
              ),
              // Content
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 60, 16, 16),
                child: Row(
                  children: [
                    // Account Avatar
                    Hero(
                      tag: 'account_${currentAccount!.id}',
                      child: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          currentAccount!.isPositive
                              ? Icons.trending_up_rounded
                              : Icons.trending_down_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Account Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            currentAccount!.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Cairo',
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'كشف الحساب',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 14,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Options Button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.more_vert, color: Colors.white),
                        onPressed: _showAccountOptions,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء كارت معلومات الحساب
  Widget _buildAccountInfoCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        elevation: 8,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Colors.grey.shade50],
            ),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4A5FBF).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.account_balance_wallet_rounded,
                      color: const Color(0xFF4A5FBF),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'نوع الحساب',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          currentAccount!.isPositive
                              ? 'حساب دائن'
                              : 'حساب مدين',
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color:
                          _accountCurrency != null
                              ? const Color(0xFF4A5FBF).withValues(alpha: 0.1)
                              : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            _accountCurrency != null
                                ? const Color(0xFF4A5FBF).withValues(alpha: 0.3)
                                : Colors.grey.shade300,
                      ),
                    ),
                    child: Text(
                      _accountCurrency?.symbol ?? 'SAR',
                      style: TextStyle(
                        color:
                            _accountCurrency != null
                                ? const Color(0xFF4A5FBF)
                                : Colors.grey.shade600,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              if (currentAccount!.phoneNumber?.isNotEmpty == true) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.phone_rounded,
                        color: Colors.green.shade600,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        currentAccount!.phoneNumber!,
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // بناء لوحة الرصيد المبتكرة
  Widget _buildBalanceDashboard() {
    if (_selectedFilterCurrency != null) {
      return _buildSingleCurrencyDashboard();
    } else {
      return _buildMultiCurrencyDashboard();
    }
  }

  // بناء لوحة رصيد عملة واحدة
  Widget _buildSingleCurrencyDashboard() {
    final summary = _getFilteredSummary();
    final currencySymbol = _selectedFilterCurrency?.symbol ?? 'SAR';
    final currencyName = _selectedFilterCurrency?.name ?? 'Saudi Riyal';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        elevation: 12,
        shadowColor: const Color(0xFF4A5FBF).withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
            ),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Currency Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الرصيد الحالي',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        currencyName,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      currencySymbol,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // Main Balance
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    summary['balance']! >= 0
                        ? Icons.trending_up_rounded
                        : Icons.trending_down_rounded,
                    color:
                        summary['balance']! >= 0
                            ? Colors.green.shade300
                            : Colors.red.shade300,
                    size: 32,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    summary['balance']!.toStringAsFixed(2),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // Summary Cards
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'دائن',
                      summary['credit']!,
                      currencySymbol,
                      Colors.green.shade300,
                      Icons.arrow_upward_rounded,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSummaryCard(
                      'مدين',
                      summary['debit']!,
                      currencySymbol,
                      Colors.red.shade300,
                      Icons.arrow_downward_rounded,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء لوحة العملات المتعددة
  Widget _buildMultiCurrencyDashboard() {
    final currencySummaries = _getCurrencySummaries();

    if (currencySummaries.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Card(
          elevation: 12,
          shadowColor: const Color(0xFF4A5FBF).withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الرصيد الحالي',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _accountCurrency?.name ?? 'Saudi Riyal',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        _accountCurrency?.symbol ?? 'SAR',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      currentAccount!.isPositive
                          ? Icons.trending_up_rounded
                          : Icons.trending_down_rounded,
                      color:
                          currentAccount!.isPositive
                              ? Colors.green.shade300
                              : Colors.red.shade300,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      currentAccount!.balance.toStringAsFixed(2),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Header Card
          Card(
            elevation: 8,
            shadowColor: const Color(0xFF4A5FBF).withValues(alpha: 0.2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
                ),
              ),
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.account_balance_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'الأرصدة حسب العملة',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Cairo',
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${currencySummaries.length} عملة مختلفة',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Currency Cards
          ...currencySummaries.entries.map((entry) {
            final currencyCode = entry.key;
            final summary = entry.value;
            final currency = usedCurrencies.firstWhere(
              (c) => c.code == currencyCode,
            );

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Card(
                elevation: 6,
                shadowColor: _getCurrencyColor(
                  currency.code,
                ).withValues(alpha: 0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        _getCurrencyColor(
                          currency.code,
                        ).withValues(alpha: 0.05),
                      ],
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _getCurrencyColor(
                                    currency.code,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  currency.symbol,
                                  style: TextStyle(
                                    color: _getCurrencyColor(currency.code),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    currency.name,
                                    style: const TextStyle(
                                      color: Colors.black87,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                  Text(
                                    '${summary['transactionCount']!.toInt()} معاملة',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    summary['balance']! >= 0
                                        ? Icons.trending_up_rounded
                                        : Icons.trending_down_rounded,
                                    color:
                                        summary['balance']! >= 0
                                            ? Colors.green.shade600
                                            : Colors.red.shade600,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    summary['balance']!.toStringAsFixed(2),
                                    style: TextStyle(
                                      color:
                                          summary['balance']! >= 0
                                              ? Colors.green.shade600
                                              : Colors.red.shade600,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                currency.symbol,
                                style: TextStyle(
                                  color: Colors.grey.shade500,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildCompactSummaryCard(
                              'دائن',
                              summary['credit']!,
                              currency.symbol,
                              Colors.green.shade600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildCompactSummaryCard(
                              'مدين',
                              summary['debit']!,
                              currency.symbol,
                              Colors.red.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  // بناء كارت ملخص
  Widget _buildSummaryCard(
    String label,
    double amount,
    String currency,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} $currency',
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }

  // بناء كارت ملخص مضغوط
  Widget _buildCompactSummaryCard(
    String label,
    double amount,
    String currency,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} $currency',
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              fontFamily: 'Cairo',
            ),
          ),
        ],
      ),
    );
  }

  // بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        elevation: 4,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'الإجراءات السريعة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      'إرسال كشف',
                      Icons.send_rounded,
                      Colors.blue.shade600,
                      _shareAccountDetails,
                      enabled: currentAccount?.phoneNumber?.isNotEmpty == true,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildActionButton(
                      'تعديل الحساب',
                      Icons.edit_rounded,
                      Colors.orange.shade600,
                      _showAccountOptions,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء زر إجراء
  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed, {
    bool enabled = true,
  }) {
    return Material(
      color: enabled ? color.withValues(alpha: 0.1) : Colors.grey.shade100,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: enabled ? onPressed : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          child: Column(
            children: [
              Icon(
                icon,
                color: enabled ? color : Colors.grey.shade400,
                size: 24,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  color: enabled ? color : Colors.grey.shade400,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Cairo',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء عنوان المعاملات
  Widget _buildTransactionsHeader() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF4A5FBF).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.receipt_long_rounded,
                  color: const Color(0xFF4A5FBF),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'المعاملات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  Text(
                    '${filteredTransactions.length} معاملة',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ],
          ),
          if (filteredTransactions.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle_rounded,
                    color: Colors.green.shade600,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'محدث',
                    style: TextStyle(
                      color: Colors.green.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // بناء قائمة المعاملات
  Widget _buildTransactionsList() {
    if (isLoading) {
      return const SliverFillRemaining(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (filteredTransactions.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(
                  Icons.receipt_long_rounded,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                _selectedFilterCurrency != null
                    ? 'لا توجد معاملات بهذه العملة'
                    : 'لا توجد معاملات',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey.shade600,
                  fontFamily: 'Cairo',
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _selectedFilterCurrency != null
                    ? 'جرب تغيير فلتر العملة'
                    : 'ابدأ بإضافة معاملة جديدة',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _addTransaction,
                icon: const Icon(Icons.add_rounded),
                label: const Text('إضافة معاملة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A5FBF),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final transaction = filteredTransactions[index];
        return Container(
          margin: EdgeInsets.fromLTRB(
            16,
            index == 0 ? 16 : 8,
            16,
            index == filteredTransactions.length - 1 ? 100 : 0,
          ),
          child: _buildTransactionCard(transaction),
        );
      }, childCount: filteredTransactions.length),
    );
  }

  // بناء كارت المعاملة المحسن
  Widget _buildTransactionCard(Transaction transaction) {
    final isCredit = transaction.type == TransactionType.credit;
    final primaryColor =
        isCredit
            ? const Color(0xFF10B981) // أخضر أنيق
            : const Color(0xFFEF4444); // أحمر أنيق
    final lightColor =
        isCredit
            ? const Color(0xFFECFDF5) // أخضر فاتح
            : const Color(0xFFFEF2F2); // أحمر فاتح

    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showTransactionDetails(transaction),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: primaryColor.withValues(alpha: 0.1),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.white, lightColor.withValues(alpha: 0.3)],
                  ),
                ),
                child: Stack(
                  children: [
                    // نمط خلفي ناعم
                    Positioned(
                      top: -20,
                      right: -20,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.05),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                    // المحتوى الرئيسي
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // الصف الرئيسي
                          Row(
                            children: [
                              // أيقونة المعاملة
                              _buildTransactionIcon(isCredit, primaryColor),
                              const SizedBox(width: 16),
                              // تفاصيل المعاملة
                              Expanded(
                                child: _buildTransactionDetails(transaction),
                              ),
                              const SizedBox(width: 16),
                              // المبلغ والعملة
                              _buildAmountSection(
                                transaction,
                                isCredit,
                                primaryColor,
                              ),
                            ],
                          ),
                          // الملاحظات (إن وجدت)
                          if (transaction.notes?.isNotEmpty == true)
                            _buildNotesSection(
                              transaction.notes!,
                              primaryColor,
                            ),
                          // شريط الإجراءات
                          _buildActionBar(transaction),
                        ],
                      ),
                    ),
                    // شريط جانبي ملون
                    Positioned(
                      left: 0,
                      top: 0,
                      bottom: 0,
                      child: Container(
                        width: 4,
                        decoration: BoxDecoration(
                          color: primaryColor,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20),
                            bottomLeft: Radius.circular(20),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // عرض تفاصيل المعاملة (دالة بسيطة للآن)
  void _showTransactionDetails(Transaction transaction) {
    // يمكن إضافة modal أو صفحة تفاصيل لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تفاصيل المعاملة: ${transaction.description}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // بناء أيقونة المعاملة
  Widget _buildTransactionIcon(bool isCredit, Color primaryColor) {
    return Hero(
      tag: 'transaction_icon_${DateTime.now().millisecondsSinceEpoch}',
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [primaryColor, primaryColor.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          isCredit ? Icons.trending_up_rounded : Icons.trending_down_rounded,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  // بناء تفاصيل المعاملة
  Widget _buildTransactionDetails(Transaction transaction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          transaction.description,
          style: const TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w700,
            color: Color(0xFF1F2937),
            fontFamily: 'Cairo',
            height: 1.2,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 6),
        Row(
          children: [
            Icon(
              Icons.access_time_rounded,
              size: 14,
              color: Colors.grey.shade500,
            ),
            const SizedBox(width: 4),
            Text(
              transaction.formattedDate,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بناء قسم المبلغ
  Widget _buildAmountSection(
    Transaction transaction,
    bool isCredit,
    Color primaryColor,
  ) {
    final currencySymbol =
        transaction.currencySymbol ?? _accountCurrency?.symbol ?? 'SAR';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: primaryColor.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isCredit ? Icons.add_rounded : Icons.remove_rounded,
                color: primaryColor,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                transaction.amount.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w800,
                  color: primaryColor,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            currencySymbol,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ),
      ],
    );
  }

  // بناء قسم الملاحظات
  Widget _buildNotesSection(String notes, Color primaryColor) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.note_rounded,
            color: primaryColor.withValues(alpha: 0.7),
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              notes,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                fontStyle: FontStyle.italic,
                fontFamily: 'Cairo',
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط الإجراءات
  Widget _buildActionBar(Transaction transaction) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildTransactionActionButton(
            icon: Icons.edit_rounded,
            label: 'تعديل',
            color: const Color(0xFF4A5FBF),
            onPressed: () => _editTransaction(transaction),
          ),
          Container(width: 1, height: 24, color: Colors.grey.shade300),
          _buildTransactionActionButton(
            icon: Icons.delete_rounded,
            label: 'حذف',
            color: const Color(0xFFEF4444),
            onPressed: () => _deleteTransaction(transaction),
          ),
        ],
      ),
    );
  }

  // بناء زر إجراء في شريط الإجراءات
  Widget _buildTransactionActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 18, color: color),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4A5FBF), Color(0xFF6B73FF)],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4A5FBF).withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _addTransaction,
        backgroundColor: Colors.transparent,
        elevation: 0,
        icon: const Icon(Icons.add_rounded, color: Colors.white),
        label: const Text(
          'إضافة معاملة',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }

  // الحصول على لون مميز لكل عملة
  Color _getCurrencyColor(String currencyCode) {
    final colors = [
      Colors.blue.shade300,
      Colors.green.shade300,
      Colors.orange.shade300,
      Colors.purple.shade300,
      Colors.teal.shade300,
      Colors.pink.shade300,
      Colors.indigo.shade300,
      Colors.amber.shade300,
    ];

    final index = currencyCode.hashCode % colors.length;
    return colors[index.abs()];
  }

  // بناء فلتر العملات
  Widget _buildCurrencyFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فلترة حسب العملة',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // زر جميع العملات
                _buildCurrencyFilterChip(
                  label: 'جميع العملات',
                  isSelected: _selectedFilterCurrency == null,
                  onTap: () => _onCurrencyFilterChanged(null),
                  icon: Icons.all_inclusive,
                ),
                const SizedBox(width: 8),
                // أزرار العملات المستخدمة
                ...usedCurrencies.map(
                  (currency) => Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: _buildCurrencyFilterChip(
                      label: '${currency.name} (${currency.symbol})',
                      isSelected: _selectedFilterCurrency?.id == currency.id,
                      onTap: () => _onCurrencyFilterChanged(currency),
                      icon: Icons.monetization_on,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريحة فلتر العملة
  Widget _buildCurrencyFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF4A5FBF) : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFF4A5FBF) : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// فئة رسم النمط الخلفي للـ Hero Section
class HeroPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.fill;

    // رسم دوائر متداخلة
    for (int i = 0; i < 3; i++) {
      final radius = (size.width * 0.3) + (i * 20);
      canvas.drawCircle(
        Offset(size.width * 0.8, size.height * 0.2),
        radius,
        paint..color = Colors.white.withValues(alpha: 0.05 - (i * 0.01)),
      );
    }

    // رسم خطوط منحنية
    final path = Path();
    path.moveTo(0, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.5,
      size.width * 0.6,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.9,
      size.width,
      size.height * 0.6,
    );
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint..color = Colors.white.withValues(alpha: 0.08));

    // رسم نقاط صغيرة
    for (int i = 0; i < 15; i++) {
      final x = (i * size.width / 15) + (i % 3) * 10;
      final y = size.height * 0.3 + (i % 2) * 20;
      canvas.drawCircle(
        Offset(x, y),
        2,
        paint..color = Colors.white.withValues(alpha: 0.3),
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
