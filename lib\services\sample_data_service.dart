import '../models/account.dart';
import '../models/transaction.dart';

class SampleDataService {
  static List<Account> getSampleAccounts() {
    return [
      Account(
        id: 'sample_1',
        name: 'أحمد محمد علي',
        phoneNumber: '**********',
        address: 'الرياض، حي النخيل',
        balance: 6900.0, // الرصيد الحالي بعد المعاملات
        initialBalance: 5000.0,
        initialIsPositive: true,
        transactionCount: 3,
        isPositive: true,
        notes: 'عميل مميز منذ 2020',
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
      ),
      Account(
        id: 'sample_2',
        name: 'فاطمة عبدالله',
        phoneNumber: '**********',
        address: 'جدة، حي الصفا',
        balance: -1050.0, // الرصيد الحالي بعد المعاملات
        initialBalance: -2500.0,
        initialIsPositive: false,
        transactionCount: 3,
        isPositive: false,
        notes: 'تاجرة مواد غذائية',
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
      ),
      Account(
        id: 'sample_3',
        name: 'محمد سعد الغامدي',
        phoneNumber: '**********',
        address: 'الدمام، حي الفيصلية',
        balance: 14150.0, // الرصيد الحالي بعد المعاملات
        initialBalance: 8750.0,
        initialIsPositive: true,
        transactionCount: 3,
        isPositive: true,
        notes: 'مقاول معتمد',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
      ),
      Account(
        id: 'sample_4',
        name: 'نورا خالد',
        phoneNumber: '**********',
        address: 'مكة المكرمة، العزيزية',
        balance: -380.0, // الرصيد الحالي بعد المعاملات
        initialBalance: -1200.0,
        initialIsPositive: false,
        transactionCount: 3,
        isPositive: false,
        notes: 'صاحبة محل خياطة',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
      ),
      Account(
        id: 'sample_5',
        name: 'عبدالرحمن التميمي',
        phoneNumber: '**********',
        address: 'المدينة المنورة، قباء',
        balance: 22700.0, // الرصيد الحالي بعد المعاملات
        initialBalance: 12000.0,
        initialIsPositive: true,
        transactionCount: 3,
        isPositive: true,
        notes: 'مستثمر عقاري',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    ];
  }

  static List<Transaction> getSampleTransactions() {
    final accounts = getSampleAccounts();
    return [
      // معاملات أحمد محمد علي
      Transaction(
        id: 'trans_1',
        accountId: accounts[0].id,
        amount: 1500.0,
        type: TransactionType.credit,
        description: 'دفعة من مشروع البناء',
        notes: 'دفعة أولى',
        date: DateTime.now().subtract(const Duration(days: 15)),
      ),
      Transaction(
        id: 'trans_2',
        accountId: accounts[0].id,
        amount: 800.0,
        type: TransactionType.debit,
        description: 'شراء مواد بناء',
        notes: 'أسمنت وحديد',
        date: DateTime.now().subtract(const Duration(days: 10)),
      ),
      Transaction(
        id: 'trans_3',
        accountId: accounts[0].id,
        amount: 2200.0,
        type: TransactionType.credit,
        description: 'دفعة ثانية من المشروع',
        notes: 'دفعة وسطى',
        date: DateTime.now().subtract(const Duration(days: 5)),
      ),

      // معاملات فاطمة عبدالله
      Transaction(
        id: 'trans_4',
        accountId: accounts[1].id,
        amount: 950.0,
        type: TransactionType.credit,
        description: 'بيع بضائع',
        notes: 'مبيعات الأسبوع',
        date: DateTime.now().subtract(const Duration(days: 12)),
      ),
      Transaction(
        id: 'trans_5',
        accountId: accounts[1].id,
        amount: 600.0,
        type: TransactionType.debit,
        description: 'شراء بضائع جديدة',
        notes: 'مواد غذائية متنوعة',
        date: DateTime.now().subtract(const Duration(days: 8)),
      ),
      Transaction(
        id: 'trans_6',
        accountId: accounts[1].id,
        amount: 1100.0,
        type: TransactionType.credit,
        description: 'مبيعات نقدية',
        notes: 'مبيعات يومية',
        date: DateTime.now().subtract(const Duration(days: 3)),
      ),

      // معاملات محمد سعد الغامدي
      Transaction(
        id: 'trans_7',
        accountId: accounts[2].id,
        amount: 5000.0,
        type: TransactionType.credit,
        description: 'عقد مقاولة جديد',
        notes: 'مشروع سكني',
        date: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Transaction(
        id: 'trans_8',
        accountId: accounts[2].id,
        amount: 2800.0,
        type: TransactionType.debit,
        description: 'رواتب العمال',
        notes: 'رواتب شهر سابق',
        date: DateTime.now().subtract(const Duration(days: 14)),
      ),
      Transaction(
        id: 'trans_9',
        accountId: accounts[2].id,
        amount: 3200.0,
        type: TransactionType.credit,
        description: 'دفعة من العميل',
        notes: 'دفعة متقدمة',
        date: DateTime.now().subtract(const Duration(days: 7)),
      ),

      // معاملات نورا خالد
      Transaction(
        id: 'trans_10',
        accountId: accounts[3].id,
        amount: 450.0,
        type: TransactionType.credit,
        description: 'خياطة فساتين',
        notes: 'طلبية خاصة',
        date: DateTime.now().subtract(const Duration(days: 9)),
      ),
      Transaction(
        id: 'trans_11',
        accountId: accounts[3].id,
        amount: 280.0,
        type: TransactionType.debit,
        description: 'شراء أقمشة',
        notes: 'أقمشة صيفية',
        date: DateTime.now().subtract(const Duration(days: 6)),
      ),
      Transaction(
        id: 'trans_12',
        accountId: accounts[3].id,
        amount: 650.0,
        type: TransactionType.credit,
        description: 'خياطة ملابس رجالية',
        notes: 'طقم كامل',
        date: DateTime.now().subtract(const Duration(days: 2)),
      ),

      // معاملات عبدالرحمن التميمي
      Transaction(
        id: 'trans_13',
        accountId: accounts[4].id,
        amount: 15000.0,
        type: TransactionType.credit,
        description: 'بيع عقار',
        notes: 'شقة في حي راقي',
        date: DateTime.now().subtract(const Duration(days: 25)),
      ),
      Transaction(
        id: 'trans_14',
        accountId: accounts[4].id,
        amount: 8500.0,
        type: TransactionType.debit,
        description: 'شراء أرض',
        notes: 'استثمار جديد',
        date: DateTime.now().subtract(const Duration(days: 18)),
      ),
      Transaction(
        id: 'trans_15',
        accountId: accounts[4].id,
        amount: 4200.0,
        type: TransactionType.credit,
        description: 'إيجار عقارات',
        notes: 'إيجارات شهرية',
        date: DateTime.now().subtract(const Duration(days: 4)),
      ),
    ];
  }

  static Map<String, dynamic> getSampleSummary() {
    final accounts = getSampleAccounts();
    final transactions = getSampleTransactions();

    // حساب الإحصائيات
    final positiveAccounts = accounts.where((a) => a.balance >= 0).length;
    final negativeAccounts = accounts.where((a) => a.balance < 0).length;

    final totalCredit = transactions
        .where((t) => t.type == TransactionType.credit)
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalDebit = transactions
        .where((t) => t.type == TransactionType.debit)
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalBalance = accounts.fold(0.0, (sum, a) => sum + a.balance);

    return {
      'totalAccounts': accounts.length,
      'positiveAccounts': positiveAccounts,
      'negativeAccounts': negativeAccounts,
      'totalCredit': totalCredit,
      'totalDebit': totalDebit,
      'totalBalance': totalBalance,
      'totalTransactions': transactions.length,
    };
  }

  static Account getSampleAccountForStatement() {
    final accounts = getSampleAccounts();
    return accounts.first; // أحمد محمد علي
  }

  static List<Transaction> getSampleTransactionsForAccount(String accountId) {
    final transactions = getSampleTransactions();
    return transactions.where((t) => t.accountId == accountId).toList();
  }

  static String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  static String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
