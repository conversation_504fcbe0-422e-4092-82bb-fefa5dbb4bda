import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/services.dart';

class SecurityManager {
  static final SecurityManager _instance = SecurityManager._internal();
  factory SecurityManager() => _instance;
  SecurityManager._internal();

  // Password management
  String hashPassword(String password) {
    final bytes = utf8.encode('$password${'salt_key_2024'}');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  bool verifyPassword(String password, String hash) {
    return hashPassword(password) == hash;
  }

  bool isPasswordStrong(String password) {
    if (password.length < 8) return false;
    if (!password.contains(RegExp(r'[A-Z]'))) return false;
    if (!password.contains(RegExp(r'[a-z]'))) return false;
    if (!password.contains(RegExp(r'[0-9]'))) return false;
    return true;
  }

  String generateRandomPassword({int length = 12}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  // Biometric authentication simulation
  Future<bool> authenticateWithBiometrics() async {
    try {
      // Simulate biometric authentication
      await Future.delayed(const Duration(seconds: 1));
      
      // In a real app, you would use local_auth package:
      // final LocalAuthentication localAuth = LocalAuthentication();
      // return await localAuth.authenticate(
      //   localizedReason: 'يرجى المصادقة للوصول إلى التطبيق',
      //   options: const AuthenticationOptions(
      //     biometricOnly: true,
      //     stickyAuth: true,
      //   ),
      // );
      
      return true; // Simulate successful authentication
    } catch (e) {
      return false;
    }
  }

  Future<bool> isBiometricAvailable() async {
    try {
      // Simulate checking biometric availability
      await Future.delayed(const Duration(milliseconds: 500));
      
      // In a real app:
      // final LocalAuthentication localAuth = LocalAuthentication();
      // return await localAuth.canCheckBiometrics;
      
      return true; // Simulate biometric availability
    } catch (e) {
      return false;
    }
  }

  Future<List<String>> getAvailableBiometrics() async {
    try {
      // Simulate getting available biometric types
      await Future.delayed(const Duration(milliseconds: 500));
      
      // In a real app:
      // final LocalAuthentication localAuth = LocalAuthentication();
      // final List<BiometricType> availableBiometrics = await localAuth.getAvailableBiometrics();
      // return availableBiometrics.map((e) => e.toString()).toList();
      
      return ['fingerprint', 'face']; // Simulate available biometrics
    } catch (e) {
      return [];
    }
  }

  // Session management
  DateTime? _lastActivity;
  Duration _sessionTimeout = const Duration(minutes: 15);

  void updateLastActivity() {
    _lastActivity = DateTime.now();
  }

  bool isSessionExpired() {
    if (_lastActivity == null) return true;
    return DateTime.now().difference(_lastActivity!) > _sessionTimeout;
  }

  void setSessionTimeout(Duration timeout) {
    _sessionTimeout = timeout;
  }

  Duration get sessionTimeout => _sessionTimeout;

  // App lock functionality
  bool _isAppLocked = false;
  int _failedAttempts = 0;
  DateTime? _lockoutTime;
  static const int maxFailedAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 5);

  bool get isAppLocked => _isAppLocked;
  int get failedAttempts => _failedAttempts;
  DateTime? get lockoutTime => _lockoutTime;

  void lockApp() {
    _isAppLocked = true;
    HapticFeedback.heavyImpact();
  }

  void unlockApp() {
    _isAppLocked = false;
    _failedAttempts = 0;
    _lockoutTime = null;
    updateLastActivity();
  }

  bool attemptUnlock(String password, String correctPasswordHash) {
    if (_lockoutTime != null && DateTime.now().isBefore(_lockoutTime!)) {
      return false; // Still in lockout period
    }

    if (verifyPassword(password, correctPasswordHash)) {
      unlockApp();
      return true;
    } else {
      _failedAttempts++;
      if (_failedAttempts >= maxFailedAttempts) {
        _lockoutTime = DateTime.now().add(lockoutDuration);
        HapticFeedback.heavyImpact();
      } else {
        HapticFeedback.lightImpact();
      }
      return false;
    }
  }

  Duration? getRemainingLockoutTime() {
    if (_lockoutTime == null) return null;
    final remaining = _lockoutTime!.difference(DateTime.now());
    return remaining.isNegative ? null : remaining;
  }

  // Data encryption (basic implementation)
  String encryptData(String data, String key) {
    // Simple XOR encryption for demonstration
    // In a real app, use proper encryption libraries
    final keyBytes = utf8.encode(key);
    final dataBytes = utf8.encode(data);
    final encrypted = <int>[];
    
    for (int i = 0; i < dataBytes.length; i++) {
      encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64.encode(encrypted);
  }

  String decryptData(String encryptedData, String key) {
    try {
      final keyBytes = utf8.encode(key);
      final encryptedBytes = base64.decode(encryptedData);
      final decrypted = <int>[];
      
      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      return '';
    }
  }

  // Security audit log
  final List<SecurityEvent> _securityLog = [];

  void logSecurityEvent(SecurityEventType type, String description) {
    _securityLog.add(SecurityEvent(
      type: type,
      description: description,
      timestamp: DateTime.now(),
    ));
    
    // Keep only last 100 events
    if (_securityLog.length > 100) {
      _securityLog.removeAt(0);
    }
  }

  List<SecurityEvent> getSecurityLog() {
    return List.unmodifiable(_securityLog);
  }

  void clearSecurityLog() {
    _securityLog.clear();
  }

  // Password strength analysis
  PasswordStrength analyzePasswordStrength(String password) {
    int score = 0;
    List<String> feedback = [];

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على 8 أحرف على الأقل');
    }

    if (password.contains(RegExp(r'[a-z]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على أحرف صغيرة');
    }

    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على أحرف كبيرة');
    }

    if (password.contains(RegExp(r'[0-9]'))) {
      score += 1;
    } else {
      feedback.add('يجب أن تحتوي على أرقام');
    }

    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      score += 1;
      feedback.clear(); // Clear feedback if all requirements are met
    } else if (score == 4) {
      feedback.add('يُنصح بإضافة رموز خاصة');
    }

    PasswordStrengthLevel level;
    if (score <= 1) {
      level = PasswordStrengthLevel.weak;
    } else if (score <= 3) {
      level = PasswordStrengthLevel.medium;
    } else if (score == 4) {
      level = PasswordStrengthLevel.strong;
    } else {
      level = PasswordStrengthLevel.veryStrong;
    }

    return PasswordStrength(
      level: level,
      score: score,
      feedback: feedback,
    );
  }
}

enum SecurityEventType {
  login,
  logout,
  failedLogin,
  passwordChange,
  settingsChange,
  dataExport,
  dataImport,
  dataDelete,
}

class SecurityEvent {
  final SecurityEventType type;
  final String description;
  final DateTime timestamp;

  SecurityEvent({
    required this.type,
    required this.description,
    required this.timestamp,
  });
}

enum PasswordStrengthLevel {
  weak,
  medium,
  strong,
  veryStrong,
}

class PasswordStrength {
  final PasswordStrengthLevel level;
  final int score;
  final List<String> feedback;

  PasswordStrength({
    required this.level,
    required this.score,
    required this.feedback,
  });
}
