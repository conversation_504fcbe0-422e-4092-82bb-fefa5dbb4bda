import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

import '../models/app_settings.dart';

class SettingsManager {
  static final SettingsManager _instance = SettingsManager._internal();
  factory SettingsManager() => _instance;
  SettingsManager._internal();

  AppSettings _settings = const AppSettings();
  bool _initialized = false;

  AppSettings get settings => _settings;

  Future<void> init() async {
    if (_initialized) return;

    // In a real app, you would load from SharedPreferences or local storage
    // For now, we'll use default settings
    _settings = const AppSettings();
    _initialized = true;
  }

  Future<void> updateSettings(AppSettings newSettings) async {
    _settings = newSettings;
    // In a real app, you would save to SharedPreferences or local storage
    await _saveToStorage();
  }

  Future<void> _saveToStorage() async {
    // Simulate saving to storage
    // In a real app: SharedPreferences.getInstance().then((prefs) => prefs.setString('settings', jsonEncode(_settings.toMap())))
  }

  // Password management
  String hashPassword(String password) {
    final bytes = utf8.encode('${password}salt_key_2024');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  bool verifyPassword(String password, String hash) {
    return hashPassword(password) == hash;
  }

  // Currency formatting
  String formatCurrency(double amount) {
    switch (_settings.selectedCurrency) {
      case 'ريال سعودي':
        return '${_formatNumber(amount)} ر.س';
      case 'دولار أمريكي':
        return '\$${_formatNumber(amount)}';
      case 'يورو':
        return '€${_formatNumber(amount)}';
      case 'جنيه مصري':
        return '${_formatNumber(amount)} ج.م';
      default:
        return '${_formatNumber(amount)} ر.س';
    }
  }

  String _formatNumber(double number) {
    switch (_settings.numberFormat) {
      case '1,234.56':
        return number
            .toStringAsFixed(2)
            .replaceAllMapped(
              RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
              (Match m) => '${m[1]},',
            );
      case '1.234,56':
        final formatted = number.toStringAsFixed(2);
        final parts = formatted.split('.');
        final integerPart = parts[0].replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]}.',
        );
        return '$integerPart,${parts[1]}';
      case '1 234.56':
        return number
            .toStringAsFixed(2)
            .replaceAllMapped(
              RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
              (Match m) => '${m[1]} ',
            );
      default:
        return number.toStringAsFixed(2);
    }
  }

  // Date formatting
  String formatDate(DateTime date) {
    switch (_settings.dateFormat) {
      case 'dd/MM/yyyy':
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case 'MM/dd/yyyy':
        return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
      case 'yyyy-MM-dd':
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      case 'dd-MM-yyyy':
        return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
      default:
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    }
  }

  // Time formatting
  String formatTime(DateTime time) {
    if (_settings.timeFormat == '12h') {
      final hour =
          time.hour > 12 ? time.hour - 12 : (time.hour == 0 ? 12 : time.hour);
      final period = time.hour >= 12 ? 'PM' : 'AM';
      return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} $period';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }

  // Theme management
  bool get isDarkMode => _settings.darkModeEnabled;

  // Notification settings
  bool get notificationsEnabled => _settings.notificationsEnabled;
  bool get soundEnabled => _settings.soundEnabled;
  bool get vibrationEnabled => _settings.vibrationEnabled;

  // Security settings
  bool get passwordEnabled => _settings.passwordEnabled;
  bool get biometricEnabled => _settings.biometricEnabled;
  String get passwordHash => _settings.passwordHash;

  // Backup settings
  bool get autoBackupEnabled => _settings.autoBackupEnabled;
  String get backupTime => _settings.backupTime;
  int get backupFrequency => _settings.backupFrequency;

  // UI settings
  double get fontSize => _settings.fontSize;
  bool get showBalanceOnHome => _settings.showBalanceOnHome;
  bool get confirmBeforeDelete => _settings.confirmBeforeDelete;

  // Reset to defaults
  Future<void> resetToDefaults() async {
    _settings = const AppSettings();
    await _saveToStorage();
  }

  // Export settings
  String exportSettings() {
    return jsonEncode(_settings.toMap());
  }

  // Import settings
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> settingsMap = jsonDecode(settingsJson);
      _settings = AppSettings.fromMap(settingsMap);
      await _saveToStorage();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Generate backup filename
  String generateBackupFilename() {
    final now = DateTime.now();
    final dateStr = formatDate(now).replaceAll('/', '-');
    final timeStr = formatTime(now).replaceAll(':', '-').replaceAll(' ', '-');
    return 'backup_${dateStr}_$timeStr.json';
  }

  // Validate password strength
  bool isPasswordStrong(String password) {
    if (password.length < 8) return false;
    if (!password.contains(RegExp(r'[A-Z]'))) return false;
    if (!password.contains(RegExp(r'[a-z]'))) return false;
    if (!password.contains(RegExp(r'[0-9]'))) return false;
    return true;
  }

  // Generate random password
  String generateRandomPassword({int length = 12}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  // Get currency symbol
  String getCurrencySymbol() {
    switch (_settings.selectedCurrency) {
      case 'ريال سعودي':
        return 'ر.س';
      case 'دولار أمريكي':
        return '\$';
      case 'يورو':
        return '€';
      case 'جنيه مصري':
        return 'ج.م';
      default:
        return 'ر.س';
    }
  }

  // Get available currencies
  List<String> getAvailableCurrencies() {
    return [
      'ريال سعودي',
      'دولار أمريكي',
      'يورو',
      'جنيه مصري',
      'درهم إماراتي',
      'دينار كويتي',
      'ريال قطري',
      'دينار بحريني',
    ];
  }

  // Get available languages
  List<String> getAvailableLanguages() {
    return ['العربية', 'English', 'Français', 'Español'];
  }

  // Get available themes
  List<String> getAvailableThemes() {
    return ['فاتح', 'مظلم', 'تلقائي', 'أزرق', 'أخضر', 'بنفسجي'];
  }

  String getThemeDisplayName(String theme) {
    switch (theme) {
      case 'light':
        return 'فاتح';
      case 'dark':
        return 'مظلم';
      case 'auto':
        return 'تلقائي';
      case 'blue':
        return 'أزرق';
      case 'green':
        return 'أخضر';
      case 'purple':
        return 'بنفسجي';
      default:
        return 'فاتح';
    }
  }
}
