import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:country_code_picker/country_code_picker.dart';
import '../models/account.dart';
import '../models/currency.dart';
import '../models/category.dart';
import '../services/app_state_service.dart';
import '../services/custom_currency_service.dart';
import '../services/category_service.dart';
import '../theme/modern_app_theme.dart';
import '../widgets/modern_ui_components.dart';
import 'add_amount_screen.dart';

/// شاشة إضافة حساب جديد بتصميم عصري ومبتكر
class AddAccountScreen extends StatefulWidget {
  const AddAccountScreen({super.key});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  bool _isPositive = true;
  String _selectedCountryCode = '+966';
  Currency? _selectedCurrency;
  List<Currency> _currencies = [];
  Category? _selectedCategory;
  List<Category> _categories = [];

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final CustomCurrencyService _currencyService = CustomCurrencyService();
  final CategoryService _categoryService = CategoryService();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCurrencies();
    _loadCategories();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  Future<void> _loadCurrencies() async {
    try {
      final currencies = await _currencyService.getAllCurrencies();
      final defaultCurrency = await _currencyService.getDefaultCurrency();

      if (mounted) {
        setState(() {
          _currencies = currencies;
          _selectedCurrency = defaultCurrency;
        });
      }
    } catch (e) {
      if (mounted) {
        ModernUIComponents.showModernSnackBar(
          context: context,
          message: 'خطأ في تحميل العملات: $e',
          isError: true,
        );
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _categoryService.getAllCategories();
      if (categories.isEmpty) {
        await _categoryService.insertDefaultCategories();
        final updatedCategories = await _categoryService.getAllCategories();
        if (mounted) {
          setState(() {
            _categories = updatedCategories;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _categories = categories;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ModernUIComponents.showModernSnackBar(
          context: context,
          message: 'خطأ في تحميل التصنيفات: $e',
          isError: true,
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار التصنيف
    if (_selectedCategory == null) {
      ModernUIComponents.showModernSnackBar(
        context: context,
        message: 'يرجى اختيار تصنيف للحساب',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final fullPhoneNumber =
          _phoneController.text.trim().isEmpty
              ? null
              : '$_selectedCountryCode${_phoneController.text.trim()}';

      final account = Account(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        phoneNumber: fullPhoneNumber,
        address:
            _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        balance: 0.0, // البدء برصيد صفر
        initialBalance: 0.0,
        initialIsPositive: _isPositive,
        transactionCount: 0,
        isPositive: _isPositive,
        createdAt: DateTime.now(),
        currencyId: _selectedCurrency?.id,
        categoryId: _selectedCategory?.id,
        categoryName: _selectedCategory?.name,
      );

      final success = await context.read<AppStateService>().addAccount(account);

      if (mounted) {
        if (success) {
          Navigator.pop(context, true);

          ModernUIComponents.showModernSnackBar(
            context: context,
            message:
                'تم إنشاء الحساب "${account.name}" في تصنيف "${_selectedCategory!.name}" بنجاح',
            isSuccess: true,
          );

          // توجيه المستخدم إلى شاشة إضافة المعاملة الافتتاحية
          await Future.delayed(const Duration(milliseconds: 500));
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AddAmountScreen(account: account),
              ),
            );
          }
        } else {
          ModernUIComponents.showModernSnackBar(
            context: context,
            message: 'فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.',
            isError: true,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ModernUIComponents.showModernSnackBar(
          context: context,
          message: 'خطأ في إنشاء الحساب: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ModernAppTheme.backgroundColor,
      appBar: _buildModernAppBar(),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [ModernAppTheme.backgroundColor, Color(0xFFF1F5F9)],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء شريط التطبيق الحديث
  PreferredSizeWidget _buildModernAppBar() {
    return ModernUIComponents.modernAppBar(
      title: 'إضافة حساب جديد',
      gradientColors: const [
        ModernAppTheme.primaryColor,
        ModernAppTheme.primaryLight,
      ],
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.help_outline_rounded, color: Colors.white),
          onPressed: _showHelpDialog,
          tooltip: 'مساعدة',
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    return CustomScrollView(
      slivers: [
        // Header Section
        SliverToBoxAdapter(child: _buildHeaderSection()),
        // Form Section
        SliverToBoxAdapter(child: _buildFormSection()),
        // Category Section
        SliverToBoxAdapter(child: _buildCategorySection()),
        // Account Type Section
        SliverToBoxAdapter(child: _buildAccountTypeSection()),
        // Currency Section
        SliverToBoxAdapter(child: _buildCurrencySection()),
        // Save Button
        SliverToBoxAdapter(child: _buildSaveButton()),
        // Bottom Spacing
        const SliverToBoxAdapter(child: SizedBox(height: 100)),
      ],
    );
  }

  /// بناء قسم الرأس
  Widget _buildHeaderSection() {
    return ModernUIComponents.modernCard(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      gradientColors: const [
        ModernAppTheme.primaryColor,
        ModernAppTheme.primaryLight,
        ModernAppTheme.accentColor,
      ],
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.person_add_rounded,
              size: 48,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: ModernAppTheme.spacingM),
          Text(
            'إنشاء حساب جديد',
            style: ModernAppTheme.headingMedium.copyWith(color: Colors.white),
          ),
          const SizedBox(height: ModernAppTheme.spacingS),
          Text(
            'أدخل بيانات الحساب الجديد لبدء تتبع المعاملات المالية',
            style: ModernAppTheme.bodyMedium.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم النموذج
  Widget _buildFormSection() {
    return ModernUIComponents.modernCard(
      margin: const EdgeInsets.symmetric(horizontal: ModernAppTheme.spacingL),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('بيانات الحساب', style: ModernAppTheme.headingSmall),
            const SizedBox(height: ModernAppTheme.spacingL),

            // اسم الحساب
            ModernUIComponents.modernTextField(
              label: 'اسم الحساب *',
              hint: 'أدخل اسم الحساب',
              prefixIcon: Icons.person_rounded,
              controller: _nameController,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الحساب';
                }
                if (value.trim().length < 2) {
                  return 'يجب أن يكون اسم الحساب أكثر من حرفين';
                }
                return null;
              },
            ),

            const SizedBox(height: ModernAppTheme.spacingL),

            // رقم الهاتف
            _buildPhoneField(),

            const SizedBox(height: ModernAppTheme.spacingL),

            // العنوان
            ModernUIComponents.modernTextField(
              label: 'العنوان (اختياري)',
              hint: 'أدخل عنوان الحساب',
              prefixIcon: Icons.location_on_rounded,
              controller: _addressController,
              maxLines: 2,
            ),

            const SizedBox(height: ModernAppTheme.spacingL),

            // الملاحظات
            ModernUIComponents.modernTextField(
              label: 'ملاحظات (اختياري)',
              hint: 'أدخل أي ملاحظات إضافية',
              prefixIcon: Icons.note_rounded,
              controller: _notesController,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حقل رقم الهاتف
  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('رقم الهاتف (اختياري)', style: ModernAppTheme.labelLarge),
        const SizedBox(height: ModernAppTheme.spacingS),
        Container(
          decoration: BoxDecoration(
            color: ModernAppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
            border: Border.all(
              color: ModernAppTheme.textTertiary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              // Country Code Picker
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: CountryCodePicker(
                  onChanged: (country) {
                    setState(() {
                      _selectedCountryCode = country.dialCode!;
                    });
                  },
                  initialSelection: 'SA',
                  favorite: const ['+966', 'SA'],
                  showCountryOnly: false,
                  showOnlyCountryWhenClosed: false,
                  alignLeft: false,
                  textStyle: ModernAppTheme.bodyMedium,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: ModernAppTheme.textTertiary.withValues(alpha: 0.3),
              ),
              // Phone Number Field
              Expanded(
                child: TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  style: ModernAppTheme.bodyLarge,
                  decoration: const InputDecoration(
                    hintText: 'رقم الهاتف',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم التصنيف
  Widget _buildCategorySection() {
    return ModernUIComponents.modernCard(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text('التصنيف', style: ModernAppTheme.headingSmall),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: ModernAppTheme.errorColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'مطلوب',
                  style: TextStyle(
                    fontSize: 10,
                    color: ModernAppTheme.errorColor,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: ModernAppTheme.spacingM),
          Text(
            'اختر التصنيف المناسب لهذا الحساب لتنظيم أفضل',
            style: ModernAppTheme.bodyMedium,
          ),
          const SizedBox(height: ModernAppTheme.spacingL),

          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color: ModernAppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
              border: Border.all(
                color:
                    _selectedCategory == null
                        ? ModernAppTheme.errorColor.withValues(alpha: 0.5)
                        : ModernAppTheme.textTertiary.withValues(alpha: 0.3),
                width: _selectedCategory == null ? 2 : 1,
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<Category>(
                value:
                    _categories.contains(_selectedCategory)
                        ? _selectedCategory
                        : null,
                isExpanded: true,
                hint: Text(
                  'اختر التصنيف *',
                  style: ModernAppTheme.bodyMedium.copyWith(
                    color:
                        _selectedCategory == null
                            ? ModernAppTheme.errorColor
                            : ModernAppTheme.textSecondary,
                  ),
                ),
                items:
                    _categories.map((category) {
                      return DropdownMenuItem<Category>(
                        value: category,
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: category.colorWithOpacity,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                category.icon,
                                color: category.color,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    category.name,
                                    style: ModernAppTheme.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    category.description,
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: ModernAppTheme.textSecondary,
                                      fontFamily: 'Cairo',
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged: (Category? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
              ),
            ),
          ),

          if (_selectedCategory == null) ...[
            const SizedBox(height: ModernAppTheme.spacingS),
            Text(
              'يرجى اختيار تصنيف للحساب',
              style: TextStyle(
                fontSize: 12,
                color: ModernAppTheme.errorColor,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قسم نوع الحساب
  Widget _buildAccountTypeSection() {
    return ModernUIComponents.modernCard(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('نوع الحساب', style: ModernAppTheme.headingSmall),
          const SizedBox(height: ModernAppTheme.spacingM),
          Text(
            'اختر نوع الحساب حسب طبيعة التعامل المالي',
            style: ModernAppTheme.bodyMedium,
          ),
          const SizedBox(height: ModernAppTheme.spacingL),

          Row(
            children: [
              Expanded(
                child: _buildAccountTypeCard(
                  title: 'حساب دائن',
                  subtitle: 'للأشخاص الذين يدينون لك',
                  icon: Icons.trending_up_rounded,
                  color: ModernAppTheme.successColor,
                  isSelected: _isPositive,
                  onTap: () => setState(() => _isPositive = true),
                ),
              ),
              const SizedBox(width: ModernAppTheme.spacingM),
              Expanded(
                child: _buildAccountTypeCard(
                  title: 'حساب مدين',
                  subtitle: 'للأشخاص الذين تدين لهم',
                  icon: Icons.trending_down_rounded,
                  color: ModernAppTheme.errorColor,
                  isSelected: !_isPositive,
                  onTap: () => setState(() => _isPositive = false),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء كارت نوع الحساب
  Widget _buildAccountTypeCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(ModernAppTheme.spacingM),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? color.withValues(alpha: 0.1)
                  : ModernAppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
          border: Border.all(
            color:
                isSelected
                    ? color
                    : ModernAppTheme.textTertiary.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: ModernAppTheme.spacingM),
            Text(
              title,
              style: ModernAppTheme.labelLarge.copyWith(
                color: isSelected ? color : ModernAppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: ModernAppTheme.spacingS),
            Text(
              subtitle,
              style: ModernAppTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم العملة
  Widget _buildCurrencySection() {
    return ModernUIComponents.modernCard(
      margin: const EdgeInsets.symmetric(horizontal: ModernAppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('العملة', style: ModernAppTheme.headingSmall),
          const SizedBox(height: ModernAppTheme.spacingM),
          Text(
            'اختر العملة المستخدمة في هذا الحساب',
            style: ModernAppTheme.bodyMedium,
          ),
          const SizedBox(height: ModernAppTheme.spacingL),

          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color: ModernAppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(ModernAppTheme.radiusL),
              border: Border.all(
                color: ModernAppTheme.textTertiary.withValues(alpha: 0.3),
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<Currency>(
                value:
                    _currencies.contains(_selectedCurrency)
                        ? _selectedCurrency
                        : null,
                isExpanded: true,
                hint: Text('اختر العملة', style: ModernAppTheme.bodyMedium),
                items:
                    _currencies.map((currency) {
                      return DropdownMenuItem<Currency>(
                        value: currency,
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: ModernAppTheme.primaryColor.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                currency.symbol,
                                style: TextStyle(
                                  color: ModernAppTheme.primaryColor,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    currency.name,
                                    style: ModernAppTheme.bodySmall,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    currency.code,
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: ModernAppTheme.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged: (Currency? newValue) {
                  setState(() {
                    _selectedCurrency = newValue;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الحفظ
  Widget _buildSaveButton() {
    return Container(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      child: ModernUIComponents.modernButton(
        text: 'إنشاء الحساب',
        onPressed: _isLoading ? null : _saveAccount,
        isLoading: _isLoading,
        isExpanded: true,
        icon: Icons.save_rounded,
        gradientColors: const [
          ModernAppTheme.primaryColor,
          ModernAppTheme.primaryLight,
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مساعدة'),
            content: const Text(
              'أدخل بيانات الحساب الجديد. جميع الحقول اختيارية عدا اسم الحساب.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }
}
