import '../database/database_helper.dart';
import '../models/category.dart';
import '../models/account.dart';

/// خدمة إدارة التصنيفات
class CategoryService {
  static final CategoryService _instance = CategoryService._internal();
  factory CategoryService() => _instance;
  CategoryService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع التصنيفات
  Future<List<Category>> getAllCategories() async {
    try {
      return await _databaseHelper.getAllCategories();
    } catch (e) {
      print('Error getting all categories: $e');
      return [];
    }
  }

  /// الحصول على تصنيف بواسطة المعرف
  Future<Category?> getCategoryById(String id) async {
    try {
      return await _databaseHelper.getCategoryById(id);
    } catch (e) {
      print('Error getting category by ID: $e');
      return null;
    }
  }

  /// إضافة تصنيف جديد
  Future<bool> insertCategory(Category category) async {
    try {
      await _databaseHelper.insertCategory(category);
      return true;
    } catch (e) {
      print('Error inserting category: $e');
      return false;
    }
  }

  /// تحديث تصنيف موجود
  Future<bool> updateCategory(Category category) async {
    try {
      await _databaseHelper.updateCategory(category);
      return true;
    } catch (e) {
      print('Error updating category: $e');
      return false;
    }
  }

  /// حذف تصنيف
  Future<bool> deleteCategory(String id) async {
    try {
      await _databaseHelper.deleteCategory(id);
      return true;
    } catch (e) {
      print('Error deleting category: $e');
      return false;
    }
  }

  /// الحصول على الحسابات حسب التصنيف
  Future<List<Account>> getAccountsByCategory(String categoryId) async {
    try {
      return await _databaseHelper.getAccountsByCategory(categoryId);
    } catch (e) {
      print('Error getting accounts by category: $e');
      return [];
    }
  }

  /// الحصول على الحسابات حسب التصنيف والعملة
  Future<List<Account>> getAccountsByCategoryAndCurrency(
    String categoryId,
    String currencyId,
  ) async {
    try {
      final allAccounts = await _databaseHelper.getAccountsByCategory(
        categoryId,
      );
      return allAccounts
          .where((account) => account.currencyId == currencyId)
          .toList();
    } catch (e) {
      print('Error getting accounts by category and currency: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات التصنيف
  Future<Map<String, dynamic>> getCategoryStatistics(String categoryId) async {
    try {
      final accounts = await _databaseHelper.getAccountsByCategory(categoryId);

      final accountCount = accounts.length;

      final currencyBalances = <String, Map<String, dynamic>>{};
      for (final account in accounts) {
        final currencyId = account.currencyId ?? 'SAR';
        if (!currencyBalances.containsKey(currencyId)) {
          currencyBalances[currencyId] = {
            'totalBalance': 0.0,
            'accountCount': 0,
          };
        }
        currencyBalances[currencyId]!['totalBalance'] =
            (currencyBalances[currencyId]!['totalBalance'] as double) +
            account.balance;
        currencyBalances[currencyId]!['accountCount'] =
            (currencyBalances[currencyId]!['accountCount'] as int) + 1;
      }

      return {
        'totalAccounts': accountCount,
        'currencyBalances': currencyBalances,
      };
    } catch (e) {
      print('Error getting category statistics: $e');
      return {};
    }
  }

  /// البحث في التصنيفات
  Future<List<Category>> searchCategories(String query) async {
    try {
      final allCategories = await _databaseHelper.getAllCategories();
      return allCategories
          .where(
            (category) =>
                category.name.toLowerCase().contains(query.toLowerCase()) ||
                category.description.toLowerCase().contains(
                  query.toLowerCase(),
                ),
          )
          .toList();
    } catch (e) {
      print('Error searching categories: $e');
      return [];
    }
  }

  /// الحصول على العملات في التصنيف
  Future<List<String>> getCurrenciesInCategory(String categoryId) async {
    try {
      final accounts = await _databaseHelper.getAccountsByCategory(categoryId);
      final currencies = <String>{};
      for (final account in accounts) {
        if (account.currencyId != null) {
          currencies.add(account.currencyId!);
        }
      }
      return currencies.toList()..sort();
    } catch (e) {
      print('Error getting currencies in category: $e');
      return [];
    }
  }

  /// التحقق من إمكانية حذف التصنيف
  Future<bool> canDeleteCategory(String categoryId) async {
    try {
      final accounts = await _databaseHelper.getAccountsByCategory(categoryId);
      return accounts.isEmpty;
    } catch (e) {
      print('Error checking if category can be deleted: $e');
      return false;
    }
  }

  /// تحديث تصنيف الحساب
  Future<void> updateAccountCategory(
    String accountId,
    String? categoryId,
    String? categoryName,
  ) async {
    try {
      final account = await _databaseHelper.getAccount(accountId);
      if (account != null) {
        final updatedAccount = account.copyWith(
          categoryId: categoryId,
          categoryName: categoryName,
        );
        await _databaseHelper.updateAccount(updatedAccount);
      }
    } catch (e) {
      print('Error updating account category: $e');
    }
  }

  /// إضافة التصنيفات الافتراضية إذا لم تكن موجودة
  Future<void> insertDefaultCategories() async {
    try {
      final categories = await getAllCategories();
      if (categories.isEmpty) {
        final defaultCategories = Category.getDefaultCategories();
        for (final category in defaultCategories) {
          await insertCategory(category);
        }
      }
    } catch (e) {
      print('Error inserting default categories: $e');
    }
  }

  /// التحقق من وجود تصنيف بالاسم
  Future<bool> categoryNameExists(String name, {String? excludeId}) async {
    try {
      final categories = await getAllCategories();
      return categories.any(
        (category) =>
            category.name.toLowerCase() == name.toLowerCase() &&
            category.id != excludeId,
      );
    } catch (e) {
      print('Error checking category name existence: $e');
      return false;
    }
  }

  /// الحصول على عدد الحسابات لكل تصنيف
  Future<Map<String, int>> getCategoryAccountCounts() async {
    try {
      final accounts = await _databaseHelper.getAllAccounts();
      final categories = await getAllCategories();
      final Map<String, int> counts = {};

      for (final category in categories) {
        counts[category.id] =
            accounts
                .where((account) => account.categoryId == category.id)
                .length;
      }

      return counts;
    } catch (e) {
      print('Error getting category account counts: $e');
      return {};
    }
  }
}
