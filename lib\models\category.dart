import 'package:flutter/material.dart';

/// نموذج التصنيف
class Category {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Category({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نسخة من التصنيف مع تحديث بعض الخصائص
  Category copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل التصنيف إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'colorValue': color.value,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  /// إنشاء تصنيف من Map
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      icon: IconData(
        map['iconCodePoint'] ?? Icons.category.codePoint,
        fontFamily: map['iconFontFamily'],
      ),
      color: Color(map['colorValue'] ?? Colors.blue.value),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: map['updatedAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
    );
  }

  /// تحويل التصنيف إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء تصنيف من JSON
  factory Category.fromJson(Map<String, dynamic> json) => Category.fromMap(json);

  @override
  String toString() {
    return 'Category(id: $id, name: $name, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// التصنيفات الافتراضية
  static List<Category> getDefaultCategories() {
    final now = DateTime.now();
    return [
      Category(
        id: 'clients',
        name: 'العملاء',
        description: 'حسابات العملاء والزبائن',
        icon: Icons.person_rounded,
        color: const Color(0xFF4A5FBF),
        createdAt: now,
      ),
      Category(
        id: 'suppliers',
        name: 'الموردين',
        description: 'حسابات الموردين والمقاولين',
        icon: Icons.business_rounded,
        color: const Color(0xFF10B981),
        createdAt: now,
      ),
      Category(
        id: 'contractors',
        name: 'المقاولين',
        description: 'حسابات المقاولين والمتعهدين',
        icon: Icons.engineering_rounded,
        color: const Color(0xFFEF4444),
        createdAt: now,
      ),
      Category(
        id: 'employees',
        name: 'الموظفين',
        description: 'حسابات الموظفين والعاملين',
        icon: Icons.badge_rounded,
        color: const Color(0xFFF59E0B),
        createdAt: now,
      ),
      Category(
        id: 'banks',
        name: 'البنوك',
        description: 'الحسابات البنكية والمالية',
        icon: Icons.account_balance_rounded,
        color: const Color(0xFF8B5CF6),
        createdAt: now,
      ),
      Category(
        id: 'others',
        name: 'أخرى',
        description: 'حسابات متنوعة أخرى',
        icon: Icons.more_horiz_rounded,
        color: const Color(0xFF6B7280),
        createdAt: now,
      ),
    ];
  }

  /// الحصول على لون التصنيف بشفافية
  Color get colorWithOpacity => color.withValues(alpha: 0.1);

  /// الحصول على لون التصنيف للحدود
  Color get borderColor => color.withValues(alpha: 0.3);

  /// التحقق من صحة بيانات التصنيف
  bool get isValid {
    return id.isNotEmpty && 
           name.isNotEmpty && 
           description.isNotEmpty;
  }

  /// الحصول على اسم التصنيف مع الوصف
  String get displayName => '$name - $description';
}
