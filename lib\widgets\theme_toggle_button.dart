import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/theme_manager.dart';

class ThemeToggleButton extends StatelessWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        final isDark = themeManager.isDarkModeActive(context);
        
        return IconButton(
          onPressed: () => themeManager.toggleTheme(),
          icon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (child, animation) {
              return RotationTransition(
                turns: animation,
                child: child,
              );
            },
            child: Icon(
              isDark ? Icons.light_mode_rounded : Icons.dark_mode_rounded,
              key: ValueKey(isDark),
              color: Colors.white,
            ),
          ),
          tooltip: isDark ? 'تبديل للوضع الفاتح' : 'تبديل للوضع المظلم',
        );
      },
    );
  }
}

class ThemeToggleSwitch extends StatelessWidget {
  const ThemeToggleSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        final isDark = themeManager.isDarkModeActive(context);
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.light_mode_rounded,
                size: 20,
                color: !isDark 
                    ? Theme.of(context).primaryColor 
                    : Theme.of(context).disabledColor,
              ),
              const SizedBox(width: 8),
              Switch(
                value: isDark,
                onChanged: (value) {
                  if (value) {
                    themeManager.setDarkMode();
                  } else {
                    themeManager.setLightMode();
                  }
                },
                activeColor: Theme.of(context).primaryColor,
                inactiveThumbColor: Theme.of(context).disabledColor,
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.dark_mode_rounded,
                size: 20,
                color: isDark 
                    ? Theme.of(context).primaryColor 
                    : Theme.of(context).disabledColor,
              ),
            ],
          ),
        );
      },
    );
  }
}

class ThemeToggleCard extends StatelessWidget {
  const ThemeToggleCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        final isDark = themeManager.isDarkModeActive(context);
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isDark ? Icons.dark_mode_rounded : Icons.light_mode_rounded,
                        color: Theme.of(context).primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'مظهر التطبيق',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            isDark ? 'الوضع المظلم' : 'الوضع الفاتح',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: isDark,
                      onChanged: (value) {
                        if (value) {
                          themeManager.setDarkMode();
                        } else {
                          themeManager.setLightMode();
                        }
                      },
                      activeColor: Theme.of(context).primaryColor,
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  isDark 
                      ? 'الوضع المظلم يوفر راحة أكبر للعين في الإضاءة المنخفضة'
                      : 'الوضع الفاتح يوفر وضوحاً أفضل في الإضاءة الطبيعية',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
