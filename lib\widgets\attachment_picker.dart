import 'dart:io';
import 'package:flutter/material.dart';
import '../services/attachment_service.dart';

class AttachmentPicker extends StatefulWidget {
  final AttachmentResult? initialAttachment;
  final Function(AttachmentResult?) onAttachmentChanged;
  final bool isRequired;

  const AttachmentPicker({
    super.key,
    this.initialAttachment,
    required this.onAttachmentChanged,
    this.isRequired = false,
  });

  @override
  State<AttachmentPicker> createState() => _AttachmentPickerState();
}

class _AttachmentPickerState extends State<AttachmentPicker> {
  AttachmentResult? _currentAttachment;
  final AttachmentService _attachmentService = AttachmentService();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentAttachment = widget.initialAttachment;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Row(
          children: [
            const Icon(
              Icons.attach_file_rounded,
              size: 20,
              color: Colors.black54,
            ),
            const SizedBox(width: 8),
            Text(
              'المرفقات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            if (widget.isRequired)
              const Text(
                ' *',
                style: TextStyle(color: Colors.red, fontSize: 16),
              ),
          ],
        ),
        const SizedBox(height: 12),

        // عرض المرفق الحالي أو أزرار الاختيار
        if (_currentAttachment != null)
          _buildAttachmentDisplay()
        else
          _buildAttachmentButtons(),
      ],
    );
  }

  // عرض المرفق المحدد
  Widget _buildAttachmentDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Row(
            children: [
              // أيقونة المرفق
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      _currentAttachment!.isImage
                          ? Colors.blue.shade100
                          : Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _attachmentService.getFileIcon(_currentAttachment!.name),
                  color:
                      _currentAttachment!.isImage
                          ? Colors.blue.shade600
                          : Colors.orange.shade600,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),

              // معلومات المرفق
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentAttachment!.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_currentAttachment!.isImage ? 'صورة' : 'مستند'} • ${_attachmentService.formatFileSize(_currentAttachment!.size)}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // زر المعاينة (للصور فقط)
                  if (_currentAttachment!.isImage)
                    IconButton(
                      onPressed: _previewAttachment,
                      icon: const Icon(Icons.visibility_rounded),
                      iconSize: 20,
                      color: Colors.blue.shade600,
                      tooltip: 'معاينة',
                    ),

                  // زر الحذف
                  IconButton(
                    onPressed: _removeAttachment,
                    icon: const Icon(Icons.delete_rounded),
                    iconSize: 20,
                    color: Colors.red.shade600,
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),

          // معاينة الصورة (إذا كانت صورة)
          if (_currentAttachment!.isImage)
            Container(
              margin: const EdgeInsets.only(top: 12),
              height: 100,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  File(_currentAttachment!.path),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey.shade200,
                      child: const Center(
                        child: Icon(Icons.broken_image, color: Colors.grey),
                      ),
                    );
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  // أزرار اختيار المرفقات
  Widget _buildAttachmentButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade300,
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        children: [
          const Icon(Icons.cloud_upload_rounded, size: 48, color: Colors.grey),
          const SizedBox(height: 12),
          const Text(
            'إضافة مرفق (اختياري)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'يمكنك إرفاق صورة أو مستند للمعاملة',
            style: TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          if (_isLoading)
            const CircularProgressIndicator()
          else
            Row(
              children: [
                // زر اختيار صورة
                Expanded(
                  child: _buildActionButton(
                    'صورة',
                    Icons.image_rounded,
                    Colors.blue,
                    _pickImage,
                  ),
                ),
                const SizedBox(width: 12),

                // زر اختيار مستند
                Expanded(
                  child: _buildActionButton(
                    'مستند',
                    Icons.description_rounded,
                    Colors.orange,
                    _pickDocument,
                  ),
                ),
                const SizedBox(width: 12),

                // زر الكاميرا
                Expanded(
                  child: _buildActionButton(
                    'كاميرا',
                    Icons.camera_alt_rounded,
                    Colors.green,
                    _captureImage,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  // بناء زر إجراء
  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // اختيار صورة من المعرض
  Future<void> _pickImage() async {
    setState(() => _isLoading = true);
    try {
      final result = await _attachmentService.pickImageFromGallery();
      if (result != null) {
        setState(() => _currentAttachment = result);
        widget.onAttachmentChanged(result);
      }
    } catch (e) {
      _showErrorDialog('خطأ في اختيار الصورة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // التقاط صورة بالكاميرا
  Future<void> _captureImage() async {
    setState(() => _isLoading = true);
    try {
      final result = await _attachmentService.captureImage();
      if (result != null) {
        setState(() => _currentAttachment = result);
        widget.onAttachmentChanged(result);
      }
    } catch (e) {
      _showErrorDialog('خطأ في التقاط الصورة: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // اختيار مستند
  Future<void> _pickDocument() async {
    setState(() => _isLoading = true);
    try {
      final result = await _attachmentService.pickDocument();
      if (result != null) {
        setState(() => _currentAttachment = result);
        widget.onAttachmentChanged(result);
      }
    } catch (e) {
      _showErrorDialog('خطأ في اختيار المستند: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // حذف المرفق
  void _removeAttachment() {
    setState(() => _currentAttachment = null);
    widget.onAttachmentChanged(null);
  }

  // معاينة المرفق
  void _previewAttachment() {
    if (_currentAttachment != null && _currentAttachment!.isImage) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => _ImagePreviewScreen(
                imagePath: _currentAttachment!.path,
                title: _currentAttachment!.name,
              ),
        ),
      );
    }
  }

  // عرض رسالة خطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}

// شاشة معاينة الصورة
class _ImagePreviewScreen extends StatelessWidget {
  final String imagePath;
  final String title;

  const _ImagePreviewScreen({required this.imagePath, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(title),
      ),
      body: Center(
        child: InteractiveViewer(
          child: Image.file(
            File(imagePath),
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.broken_image, color: Colors.white, size: 64),
                    SizedBox(height: 16),
                    Text(
                      'لا يمكن عرض الصورة',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
