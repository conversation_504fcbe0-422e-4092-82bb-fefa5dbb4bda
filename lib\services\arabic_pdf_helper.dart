import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'arabic_font_service.dart';

class ArabicPdfHelper {
  static final ArabicFontService _fontService = ArabicFontService();

  // Initialize Arabic fonts
  static Future<void> initializeFonts() async {
    await _fontService.loadArabicFonts();
  }

  // Create Arabic text widget
  static pw.Text arabicText(
    String text, {
    double fontSize = 12,
    bool bold = false,
    PdfColor? color,
    pw.TextAlign? textAlign,
  }) {
    return pw.Text(
      text,
      style: _fontService.getArabicTextStyle(
        fontSize: fontSize,
        bold: bold,
        color: color,
      ),
      textAlign: textAlign ?? pw.TextAlign.right,
      textDirection: pw.TextDirection.rtl,
    );
  }

  // Create Arabic header widget
  static pw.Text arabicHeader(
    String text, {
    double fontSize = 18,
    PdfColor? color,
    pw.TextAlign? textAlign,
  }) {
    return pw.Text(
      text,
      style: _fontService.getArabicHeaderStyle(
        fontSize: fontSize,
        color: color,
      ),
      textAlign: textAlign ?? pw.TextAlign.right,
      textDirection: pw.TextDirection.rtl,
    );
  }

  // Create table header cell with Arabic text
  static pw.Widget tableHeaderCell(String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: arabicText(text, bold: true, fontSize: 12),
    );
  }

  // Create table data cell with Arabic text
  static pw.Widget tableDataCell(
    String text, {
    PdfColor? textColor,
    bool bold = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: arabicText(text, color: textColor, bold: bold, fontSize: 10),
    );
  }

  // Create modern Arabic document header
  static pw.Widget createDocumentHeader({
    required String title,
    required String subtitle,
    required String date,
    PdfColor? backgroundColor,
    PdfColor? textColor,
  }) {
    return pw.Container(
      width: double.infinity,
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            backgroundColor ?? PdfColors.blue600,
            (backgroundColor ?? PdfColors.blue600).shade(0.8),
          ],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(15),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Stack(
        children: [
          // Background pattern
          pw.Positioned(
            right: -20,
            top: -20,
            child: pw.Container(
              width: 100,
              height: 100,
              decoration: pw.BoxDecoration(
                color: PdfColors.white.shade(0.1),
                shape: pw.BoxShape.circle,
              ),
            ),
          ),
          pw.Positioned(
            left: -30,
            bottom: -30,
            child: pw.Container(
              width: 80,
              height: 80,
              decoration: pw.BoxDecoration(
                color: PdfColors.white.shade(0.05),
                shape: pw.BoxShape.circle,
              ),
            ),
          ),
          // Content
          pw.Padding(
            padding: const pw.EdgeInsets.all(25),
            child: pw.Row(
              children: [
                // Icon section
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.white.shade(0.2),
                    borderRadius: pw.BorderRadius.circular(12),
                  ),
                  child: pw.Icon(
                    pw.IconData(0xe88a), // document icon
                    color: PdfColors.white,
                    size: 32,
                  ),
                ),
                pw.SizedBox(width: 20),
                // Text content
                pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      arabicHeader(title, fontSize: 28, color: PdfColors.white),
                      pw.SizedBox(height: 8),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.white.shade(0.2),
                          borderRadius: pw.BorderRadius.circular(20),
                        ),
                        child: arabicText(
                          subtitle,
                          fontSize: 14,
                          color: PdfColors.white,
                        ),
                      ),
                      pw.SizedBox(height: 8),
                      arabicText(
                        date,
                        fontSize: 12,
                        color: PdfColors.white.shade(0.9),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Create modern summary section
  static pw.Widget createSummarySection({
    required String title,
    required List<String> summaryItems,
  }) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [PdfColors.grey50, PdfColors.grey100],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Title with icon
          pw.Row(
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                decoration: pw.BoxDecoration(
                  gradient: pw.LinearGradient(
                    colors: [PdfColors.blue600, PdfColors.blue700],
                  ),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Icon(
                  pw.IconData(0xe8b8), // analytics icon
                  color: PdfColors.white,
                  size: 16,
                ),
              ),
              pw.SizedBox(width: 12),
              arabicHeader(title, fontSize: 18, color: PdfColors.blue900),
            ],
          ),
          pw.SizedBox(height: 15),
          // Summary items
          ...summaryItems.map(
            (item) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 8),
              padding: const pw.EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              decoration: pw.BoxDecoration(
                color: PdfColors.white,
                borderRadius: pw.BorderRadius.circular(8),
                border: pw.Border.all(color: PdfColors.blue200, width: 1),
              ),
              child: pw.Row(
                children: [
                  pw.Container(
                    width: 6,
                    height: 6,
                    decoration: pw.BoxDecoration(
                      color: PdfColors.blue600,
                      shape: pw.BoxShape.circle,
                    ),
                  ),
                  pw.SizedBox(width: 8),
                  arabicText(item, fontSize: 11, color: PdfColors.grey800),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Create table with Arabic headers
  static pw.Table createArabicTable({
    required List<String> headers,
    required List<List<String>> data,
    List<PdfColor?>? rowColors,
    List<bool>? boldColumns,
  }) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300, width: 0.5),
      children: [
        // Header row with gradient
        pw.TableRow(
          decoration: pw.BoxDecoration(
            gradient: pw.LinearGradient(
              colors: [PdfColors.blue600, PdfColors.blue700],
              begin: pw.Alignment.topLeft,
              end: pw.Alignment.bottomRight,
            ),
          ),
          children:
              headers
                  .map(
                    (header) => pw.Container(
                      padding: const pw.EdgeInsets.all(12),
                      child: arabicText(
                        header,
                        bold: true,
                        fontSize: 12,
                        color: PdfColors.white,
                      ),
                    ),
                  )
                  .toList(),
        ),
        // Data rows with alternating colors
        ...data.asMap().entries.map((entry) {
          final index = entry.key;
          final row = entry.value;
          final isEven = index % 2 == 0;

          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white,
            ),
            children:
                row.asMap().entries.map((cellEntry) {
                  final colIndex = cellEntry.key;
                  final cell = cellEntry.value;
                  final isBold =
                      boldColumns != null &&
                      colIndex < boldColumns.length &&
                      boldColumns[colIndex];
                  final textColor =
                      rowColors != null && colIndex < rowColors.length
                          ? rowColors[colIndex]
                          : PdfColors.grey800;

                  return pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    child: arabicText(
                      cell,
                      color: textColor,
                      bold: isBold,
                      fontSize: 10,
                    ),
                  );
                }).toList(),
          );
        }),
      ],
    );
  }

  // Create modern empty state widget
  static pw.Widget createEmptyState(String message) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(30),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [PdfColors.grey50, PdfColors.grey100],
          begin: pw.Alignment.topCenter,
          end: pw.Alignment.bottomCenter,
        ),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.grey300, width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey300,
              shape: pw.BoxShape.circle,
            ),
            child: pw.Icon(
              pw.IconData(0xe8b6), // folder open icon
              color: PdfColors.grey600,
              size: 32,
            ),
          ),
          pw.SizedBox(height: 15),
          arabicText(
            message,
            fontSize: 16,
            color: PdfColors.grey700,
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }
}
