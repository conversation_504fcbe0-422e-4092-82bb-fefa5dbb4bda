import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/app_settings.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  bool _isInitialized = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // Initialize notification service
  Future<void> initialize(AppSettings settings) async {
    if (_isInitialized) return;

    _notificationsEnabled = settings.notificationsEnabled;
    _soundEnabled = settings.soundEnabled;
    _vibrationEnabled = settings.vibrationEnabled;

    // In a real app, you would initialize actual notification service here
    // For example: firebase_messaging, flutter_local_notifications, etc.
    
    _isInitialized = true;
    debugPrint('Notification Service initialized');
  }

  // Update notification settings
  void updateSettings(AppSettings settings) {
    _notificationsEnabled = settings.notificationsEnabled;
    _soundEnabled = settings.soundEnabled;
    _vibrationEnabled = settings.vibrationEnabled;
    
    debugPrint('Notification settings updated: '
        'notifications: $_notificationsEnabled, '
        'sound: $_soundEnabled, '
        'vibration: $_vibrationEnabled');
  }

  // Show notification
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    NotificationType type = NotificationType.info,
  }) async {
    if (!_notificationsEnabled) return;

    // Play sound if enabled
    if (_soundEnabled) {
      await _playNotificationSound(type);
    }

    // Vibrate if enabled
    if (_vibrationEnabled) {
      await _vibrate(type);
    }

    // Show visual notification
    await _showVisualNotification(title, body, type);

    debugPrint('Notification shown: $title - $body');
  }

  // Play notification sound
  Future<void> _playNotificationSound(NotificationType type) async {
    try {
      // In a real app, you would play actual sound files
      // For web, you can use HTML5 audio or Web Audio API
      
      switch (type) {
        case NotificationType.success:
          await SystemSound.play(SystemSoundType.click);
          break;
        case NotificationType.error:
          await SystemSound.play(SystemSoundType.alert);
          break;
        case NotificationType.warning:
          await SystemSound.play(SystemSoundType.click);
          break;
        case NotificationType.info:
          await SystemSound.play(SystemSoundType.click);
          break;
      }
    } catch (e) {
      debugPrint('Error playing notification sound: $e');
    }
  }

  // Vibrate device
  Future<void> _vibrate(NotificationType type) async {
    try {
      switch (type) {
        case NotificationType.success:
          await HapticFeedback.lightImpact();
          break;
        case NotificationType.error:
          await HapticFeedback.heavyImpact();
          break;
        case NotificationType.warning:
          await HapticFeedback.mediumImpact();
          break;
        case NotificationType.info:
          await HapticFeedback.selectionClick();
          break;
      }
    } catch (e) {
      debugPrint('Error vibrating device: $e');
    }
  }

  // Show visual notification
  Future<void> _showVisualNotification(
    String title,
    String body,
    NotificationType type,
  ) async {
    // In a real app, you would show actual system notifications
    // For web, you can use the Notification API
    
    if (kIsWeb) {
      // Web notification implementation would go here
      debugPrint('Web notification: $title - $body');
    } else {
      // Mobile notification implementation would go here
      debugPrint('Mobile notification: $title - $body');
    }
  }

  // Show transaction notification
  Future<void> showTransactionNotification({
    required String accountName,
    required double amount,
    required bool isCredit,
  }) async {
    final String title = isCredit ? 'دفعة جديدة' : 'مصروف جديد';
    final String body = '$accountName: ${amount.toStringAsFixed(0)} ر.س';
    
    await showNotification(
      title: title,
      body: body,
      type: isCredit ? NotificationType.success : NotificationType.info,
    );
  }

  // Show backup notification
  Future<void> showBackupNotification({
    required bool success,
    String? message,
  }) async {
    final String title = success ? 'تم النسخ الاحتياطي' : 'فشل النسخ الاحتياطي';
    final String body = message ?? (success ? 'تم إنشاء النسخة الاحتياطية بنجاح' : 'حدث خطأ أثناء النسخ الاحتياطي');
    
    await showNotification(
      title: title,
      body: body,
      type: success ? NotificationType.success : NotificationType.error,
    );
  }

  // Show security notification
  Future<void> showSecurityNotification({
    required String event,
    String? details,
  }) async {
    await showNotification(
      title: 'تنبيه أمني',
      body: details ?? event,
      type: NotificationType.warning,
    );
  }

  // Show reminder notification
  Future<void> showReminderNotification({
    required String title,
    required String message,
  }) async {
    await showNotification(
      title: title,
      body: message,
      type: NotificationType.info,
    );
  }

  // Schedule notification (for future implementation)
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    NotificationType type = NotificationType.info,
  }) async {
    // Implementation for scheduled notifications
    debugPrint('Notification scheduled for $scheduledTime: $title - $body');
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    // Implementation to cancel all pending notifications
    debugPrint('All notifications cancelled');
  }

  // Request notification permissions (for mobile)
  Future<bool> requestPermissions() async {
    // Implementation to request notification permissions
    // This would be platform-specific
    return true;
  }

  // Check if notifications are supported
  bool get isSupported {
    // Check if the platform supports notifications
    return true; // Simplified for this example
  }

  // Get notification settings
  Map<String, bool> get settings {
    return {
      'notifications': _notificationsEnabled,
      'sound': _soundEnabled,
      'vibration': _vibrationEnabled,
    };
  }
}

enum NotificationType {
  success,
  error,
  warning,
  info,
}

// Notification data model
class NotificationData {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final DateTime timestamp;
  final String? payload;
  final bool isRead;

  NotificationData({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.timestamp,
    this.payload,
    this.isRead = false,
  });

  NotificationData copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    DateTime? timestamp,
    String? payload,
    bool? isRead,
  }) {
    return NotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      payload: payload ?? this.payload,
      isRead: isRead ?? this.isRead,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.toString(),
      'timestamp': timestamp.toIso8601String(),
      'payload': payload,
      'isRead': isRead,
    };
  }

  factory NotificationData.fromMap(Map<String, dynamic> map) {
    return NotificationData(
      id: map['id'],
      title: map['title'],
      body: map['body'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => NotificationType.info,
      ),
      timestamp: DateTime.parse(map['timestamp']),
      payload: map['payload'],
      isRead: map['isRead'] ?? false,
    );
  }
}
