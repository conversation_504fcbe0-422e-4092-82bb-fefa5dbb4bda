import 'package:flutter/material.dart';
import '../services/responsive_service.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;
  final Widget? fallback;

  const ResponsiveLayout({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveService.getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? tablet ?? desktop ?? largeDesktop ?? fallback ?? const SizedBox();
      case DeviceType.tablet:
        return tablet ?? mobile ?? desktop ?? largeDesktop ?? fallback ?? const SizedBox();
      case DeviceType.desktop:
        return desktop ?? tablet ?? largeDesktop ?? mobile ?? fallback ?? const SizedBox();
      case DeviceType.largeDesktop:
        return largeDesktop ?? desktop ?? tablet ?? mobile ?? fallback ?? const SizedBox();
    }
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveService.getDeviceType(context);
    return builder(context, deviceType);
  }
}

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final BoxConstraints? constraints;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.width,
    this.height,
    this.constraints,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? ResponsiveService.getResponsivePadding(context),
      margin: margin ?? ResponsiveService.getResponsiveMargin(context),
      color: color,
      decoration: decoration,
      width: width,
      height: height,
      constraints: constraints ?? ResponsiveService.getResponsiveConstraints(context),
      child: child,
    );
  }
}

class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final double? elevation;
  final BorderRadius? borderRadius;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveBorderRadius = ResponsiveService.getResponsiveBorderRadius(context, 12);
    final responsiveElevation = ResponsiveService.getResponsiveElevation(context, 4);

    return Container(
      width: ResponsiveService.getResponsiveCardWidth(context),
      margin: margin ?? ResponsiveService.getResponsiveMargin(context),
      child: Card(
        elevation: elevation ?? responsiveElevation,
        color: color,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(responsiveBorderRadius),
        ),
        child: Padding(
          padding: padding ?? ResponsiveService.getResponsivePadding(context),
          child: child,
        ),
      ),
    );
  }
}

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.fontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveFontSize = ResponsiveService.getResponsiveFontSize(
      context,
      fontSize ?? 16,
    );

    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: responsiveFontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;

  const ResponsiveIcon(
    this.icon, {
    super.key,
    this.size,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveSize = ResponsiveService.getResponsiveIconSize(
      context,
      size ?? 24,
    );

    return Icon(
      icon,
      size: responsiveSize,
      color: color,
    );
  }
}

class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final double? height;
  final double? width;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.style,
    this.height,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveHeight = ResponsiveService.getResponsiveButtonHeight(context);

    return SizedBox(
      height: height ?? responsiveHeight,
      width: width,
      child: ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: child,
      ),
    );
  }
}

class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveService.getResponsiveGridColumns(context);
    final spacing = ResponsiveService.getResponsiveSpacing(context, 16);

    return GridView.count(
      crossAxisCount: columns,
      childAspectRatio: childAspectRatio ?? 1.0,
      mainAxisSpacing: mainAxisSpacing ?? spacing,
      crossAxisSpacing: crossAxisSpacing ?? spacing,
      padding: padding ?? ResponsiveService.getResponsivePadding(context),
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

class ResponsiveSizedBox extends StatelessWidget {
  final double? width;
  final double? height;
  final Widget? child;

  const ResponsiveSizedBox({
    super.key,
    this.width,
    this.height,
    this.child,
  });

  const ResponsiveSizedBox.height(double height, {super.key})
      : width = null,
        height = height,
        child = null;

  const ResponsiveSizedBox.width(double width, {super.key})
      : width = width,
        height = null,
        child = null;

  @override
  Widget build(BuildContext context) {
    final responsiveWidth = width != null 
        ? ResponsiveService.getResponsiveSpacing(context, width!)
        : null;
    final responsiveHeight = height != null 
        ? ResponsiveService.getResponsiveSpacing(context, height!)
        : null;

    return SizedBox(
      width: responsiveWidth,
      height: responsiveHeight,
      child: child,
    );
  }
}

class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final double? elevation;

  const ResponsiveAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      elevation: elevation,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    ResponsiveService.getResponsiveAppBarHeight(
      // Note: We can't access context here, so we use a default value
      // This should be handled differently in a real implementation
      WidgetsBinding.instance.focusManager.primaryFocus?.context ?? 
      WidgetsBinding.instance.rootElement!,
    ),
  );
}
