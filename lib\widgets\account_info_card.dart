import 'package:flutter/material.dart';
import '../models/account.dart';
import '../theme/app_theme.dart';
import 'modern_card.dart';

class AccountInfoCard extends StatelessWidget {
  final Account account;
  final VoidCallback? onEdit;

  const AccountInfoCard({super.key, required this.account, this.onEdit});

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with name and edit button
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات الحساب',
                      style: TextStyle(
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                        fontSize: ResponsiveHelper.getResponsiveFontSize(
                          context,
                          12,
                        ),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      account.name,
                      style: TextStyle(
                        color: Theme.of(context).textTheme.titleLarge?.color,
                        fontSize: ResponsiveHelper.getResponsiveFontSize(
                          context,
                          18,
                        ),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              if (onEdit != null)
                Container(
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: onEdit,
                    icon: const Icon(
                      Icons.edit_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                    tooltip: 'تعديل المعلومات',
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Account details
          _buildInfoRow(
            context: context,
            icon: Icons.account_balance_wallet_rounded,
            label: 'الرصيد الحالي',
            value: '${account.formattedBalance} جنيه',
            valueColor:
                account.isPositive
                    ? AppTheme.successColor
                    : AppTheme.errorColor,
            gradient:
                account.isPositive
                    ? AppTheme.successGradient
                    : AppTheme.errorGradient,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            context: context,
            icon: Icons.trending_up_rounded,
            label: 'الرصيد الابتدائي',
            value: '${account.initialBalance.toStringAsFixed(0)} جنيه',
            valueColor:
                account.initialIsPositive
                    ? AppTheme.successColor
                    : AppTheme.errorColor,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            context: context,
            icon: Icons.swap_horiz_rounded,
            label: 'عدد المعاملات',
            value: '${account.transactionCount} معاملة',
            valueColor:
                Theme.of(context).textTheme.titleMedium?.color ??
                AppTheme.textPrimary,
          ),

          if (account.phoneNumber != null &&
              account.phoneNumber!.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              context: context,
              icon: Icons.phone_rounded,
              label: 'رقم الهاتف',
              value: account.phoneNumber!,
              valueColor:
                  Theme.of(context).textTheme.titleMedium?.color ??
                  AppTheme.textPrimary,
            ),
          ],

          if (account.address != null && account.address!.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              context: context,
              icon: Icons.location_on_rounded,
              label: 'العنوان',
              value: account.address!,
              valueColor:
                  Theme.of(context).textTheme.titleMedium?.color ??
                  AppTheme.textPrimary,
              isMultiline: true,
            ),
          ],

          if (account.notes != null && account.notes!.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              context: context,
              icon: Icons.note_rounded,
              label: 'ملاحظات',
              value: account.notes!,
              valueColor:
                  Theme.of(context).textTheme.titleMedium?.color ??
                  AppTheme.textPrimary,
              isMultiline: true,
            ),
          ],

          const SizedBox(height: 12),

          _buildInfoRow(
            context: context,
            icon: Icons.calendar_today_rounded,
            label: 'تاريخ الإنشاء',
            value: account.formattedCreatedDate,
            valueColor:
                Theme.of(context).textTheme.bodyMedium?.color ??
                AppTheme.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    required Color valueColor,
    LinearGradient? gradient,
    bool isMultiline = false,
  }) {
    return Row(
      crossAxisAlignment:
          isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: gradient ?? AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  color: valueColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: isMultiline ? null : 1,
                overflow: isMultiline ? null : TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
