import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/company_info.dart';
import '../database/database_helper.dart';
import '../services/custom_currency_service.dart';
import '../services/company_info_service.dart';
import 'arabic_font_service.dart';
import 'font_fallback_service.dart';

class ReportService {
  static final ReportService _instance = ReportService._internal();
  factory ReportService() => _instance;
  ReportService._internal();

  // App theme colors
  static const PdfColor _primaryColor = PdfColor.fromInt(0xFF4A5FBF);
  static const PdfColor _lightBlue = PdfColor.fromInt(0xFFE3F2FD);
  static const PdfColor _darkBlue = PdfColor.fromInt(0xFF1565C0);
  static const PdfColor _successColor = PdfColor.fromInt(0xFF4CAF50);
  static const PdfColor _errorColor = PdfColor.fromInt(0xFFF44336);
  static const PdfColor _warningColor = PdfColor.fromInt(0xFFFF9800);
  static const PdfColor _greyLight = PdfColor.fromInt(0xFFF5F5F5);
  static const PdfColor _greyDark = PdfColor.fromInt(0xFF757575);

  final DatabaseHelper _storage = DatabaseHelper();
  final ArabicFontService _fontService = ArabicFontService();
  final CustomCurrencyService _currencyService = CustomCurrencyService();
  final CompanyInfoService _companyInfoService = CompanyInfoService();
  final FontFallbackService _fallbackFontService = FontFallbackService();

  // Build professional company header for PDF reports
  Future<pw.Widget> _buildCompanyHeader({
    required String reportTitle,
    String? subtitle,
    PdfColor? backgroundColor,
    PdfColor? titleColor,
  }) async {
    final companyInfo = await _companyInfoService.getCompanyInfoForReports();
    final logoBytes = await _companyInfoService.getLogoBytes();
    final now = DateTime.now();
    final dateFormatter = DateFormat('dd/MM/yyyy', 'ar');
    final timeFormatter = DateFormat('HH:mm', 'ar');

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: backgroundColor ?? _lightBlue,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: _primaryColor, width: 2),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Company Logo Section
          if (logoBytes != null) ...[
            pw.Container(
              width: 80,
              height: 80,
              decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(10),
                border: pw.Border.all(color: _primaryColor, width: 1),
                color: PdfColors.white,
              ),
              child: pw.Padding(
                padding: const pw.EdgeInsets.all(4),
                child: pw.Image(
                  pw.MemoryImage(logoBytes),
                  fit: pw.BoxFit.contain,
                ),
              ),
            ),
            pw.SizedBox(width: 20),
          ],

          // Company Information Section
          pw.Expanded(
            flex: 3,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Company Name
                pw.Text(
                  companyInfo.name,
                  style: _fontService.getArabicHeaderStyle(
                    fontSize: 22,
                    color: titleColor ?? _darkBlue,
                  ),
                ),

                pw.SizedBox(height: 6),

                // Registration Number
                if (companyInfo.registrationNumber.isNotEmpty) ...[
                  pw.Row(
                    children: [
                      pw.Text(
                        'رقم السجل التجاري: ',
                        style: _fontService.getArabicTextStyle(
                          fontSize: 11,
                          color: _greyDark,
                        ),
                      ),
                      pw.Text(
                        companyInfo.registrationNumber,
                        style: _fontService.getArabicTextStyle(
                          fontSize: 11,
                          color: _primaryColor,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 4),
                ],

                // Address
                if (companyInfo.address.isNotEmpty) ...[
                  pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'العنوان: ',
                        style: _fontService.getArabicTextStyle(
                          fontSize: 10,
                          color: _greyDark,
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Text(
                          companyInfo.formattedAddress,
                          style: _fontService.getArabicTextStyle(
                            fontSize: 10,
                            color: PdfColors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 4),
                ],

                // Contact Information
                if (companyInfo.contactInfo.isNotEmpty) ...[
                  pw.Text(
                    companyInfo.contactInfo,
                    style: _fontService.getArabicTextStyle(
                      fontSize: 10,
                      color: _greyDark,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Report Information Section
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                // Report Title
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: pw.BoxDecoration(
                    color: _primaryColor,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Text(
                    reportTitle,
                    style: _fontService.getArabicHeaderStyle(
                      fontSize: 16,
                      color: PdfColors.white,
                    ),
                  ),
                ),

                pw.SizedBox(height: 8),

                // Subtitle
                if (subtitle != null) ...[
                  pw.Text(
                    subtitle,
                    style: _fontService.getArabicTextStyle(
                      fontSize: 12,
                      color: _greyDark,
                    ),
                    textAlign: pw.TextAlign.right,
                  ),
                  pw.SizedBox(height: 8),
                ],

                // Date and Time
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    color: _greyLight,
                    borderRadius: pw.BorderRadius.circular(6),
                    border: pw.Border.all(color: _greyDark),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'تاريخ التقرير',
                        style: _fontService.getArabicTextStyle(
                          fontSize: 9,
                          color: _greyDark,
                        ),
                      ),
                      pw.Text(
                        dateFormatter.format(now),
                        style: _fontService.getArabicTextStyle(
                          fontSize: 12,
                          color: _primaryColor,
                        ),
                      ),
                      pw.SizedBox(height: 2),
                      pw.Text(
                        timeFormatter.format(now),
                        style: _fontService.getArabicTextStyle(
                          fontSize: 10,
                          color: _greyDark,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build table header style
  pw.TextStyle _getTableHeaderStyle() {
    return _fontService.getArabicTextStyle(
      fontSize: 12,
      color: PdfColors.white,
    );
  }

  // Build table cell style
  pw.TextStyle _getTableCellStyle({bool isEven = false}) {
    return _fontService.getArabicTextStyle(
      fontSize: 11,
      color: PdfColors.black,
    );
  }

  // Get table row color
  PdfColor _getTableRowColor(int index) {
    return index.isEven ? PdfColors.white : _greyLight;
  }

  // Create reports directory
  Future<Directory> _getReportsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final reportsDir = Directory('${appDir.path}/reports');
    if (!await reportsDir.exists()) {
      await reportsDir.create(recursive: true);
    }
    return reportsDir;
  }

  // Share file
  Future<void> _shareFile(File file, String title) async {
    await Share.shareXFiles([XFile(file.path)], text: title);
  }

  // ==================== PDF REPORTS ====================

  // Generate All Accounts PDF Report
  Future<File> generateAccountsPDFReport() async {
    try {
      // Load Arabic fonts and fallback fonts
      await _fontService.loadArabicFonts();
      await _fallbackFontService.initializeFallbackFonts();

      final pdf = pw.Document();
      final accounts = await _storage.getAllAccounts();
      final currencySymbol = await _currencyService.getDefaultCurrencySymbol();

      // Calculate totals
      double totalPositive = 0;
      double totalNegative = 0;
      for (final account in accounts) {
        if (account.balance >= 0) {
          totalPositive += account.balance;
        } else {
          totalNegative += account.balance.abs();
        }
      }

      // Build company header
      final companyHeader = await _buildCompanyHeader(
        reportTitle: 'تقرير الحسابات الشامل',
        subtitle:
            'عدد الحسابات: ${accounts.length} | إجمالي الأرصدة الموجبة: ${totalPositive.toStringAsFixed(2)} $currencySymbol',
        backgroundColor: _lightBlue,
        titleColor: _darkBlue,
      );

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return [
              // Company Header
              companyHeader,

              pw.SizedBox(height: 30),

              // Summary Section
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: _greyLight,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: _greyDark),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                  children: [
                    _buildSummaryCard(
                      'إجمالي الحسابات',
                      accounts.length.toString(),
                      _primaryColor,
                    ),
                    _buildSummaryCard(
                      'الأرصدة الموجبة',
                      '${totalPositive.toStringAsFixed(2)} $currencySymbol',
                      _successColor,
                    ),
                    _buildSummaryCard(
                      'الأرصدة السالبة',
                      '${totalNegative.toStringAsFixed(2)} $currencySymbol',
                      _errorColor,
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Accounts Table
              if (accounts.isNotEmpty) ...[
                pw.Text(
                  'تفاصيل الحسابات',
                  style: _fontService.getArabicHeaderStyle(
                    fontSize: 18,
                    color: _darkBlue,
                  ),
                ),

                pw.SizedBox(height: 10),

                pw.Table(
                  border: pw.TableBorder.all(color: _greyDark),
                  columnWidths: {
                    0: const pw.FlexColumnWidth(3), // Account Name
                    1: const pw.FlexColumnWidth(2), // Balance
                    2: const pw.FlexColumnWidth(1.5), // Type
                    3: const pw.FlexColumnWidth(1.5), // Transactions
                    4: const pw.FlexColumnWidth(2), // Created Date
                  },
                  children: [
                    // Header Row
                    pw.TableRow(
                      decoration: pw.BoxDecoration(color: _primaryColor),
                      children: [
                        _buildTableHeader('اسم الحساب'),
                        _buildTableHeader('الرصيد'),
                        _buildTableHeader('النوع'),
                        _buildTableHeader('المعاملات'),
                        _buildTableHeader('تاريخ الإنشاء'),
                      ],
                    ),

                    // Data Rows
                    ...accounts.asMap().entries.map((entry) {
                      final index = entry.key;
                      final account = entry.value;
                      final transactionCount = 0; // Will be calculated

                      return pw.TableRow(
                        decoration: pw.BoxDecoration(
                          color: _getTableRowColor(index),
                        ),
                        children: [
                          _buildTableCell(account.name),
                          _buildTableCell(
                            '${account.balance.toStringAsFixed(2)} $currencySymbol',
                            textColor:
                                account.balance >= 0
                                    ? _successColor
                                    : _errorColor,
                          ),
                          _buildTableCell(account.typeInArabic),
                          _buildTableCell('$transactionCount'),
                          _buildTableCell(
                            DateFormat(
                              'dd/MM/yyyy',
                            ).format(account.createdAt ?? DateTime.now()),
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ] else ...[
                pw.Center(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(20),
                    child: pw.Text(
                      'لا توجد حسابات مسجلة',
                      style: _fontService.getArabicTextStyle(
                        fontSize: 16,
                        color: _greyDark,
                      ),
                    ),
                  ),
                ),
              ],
            ];
          },
        ),
      );

      // Save file
      final reportsDir = await _getReportsDirectory();
      final fileName =
          'تقرير_الحسابات_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final file = File('${reportsDir.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      return file;
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير PDF: $e');
    }
  }

  // Build summary card for PDF
  pw.Widget _buildSummaryCard(String title, String value, PdfColor color) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: color),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            title,
            style: _fontService.getArabicTextStyle(
              fontSize: 10,
              color: _greyDark,
            ),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            value,
            style: _fontService.getArabicTextStyle(fontSize: 12, color: color),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build table header cell
  pw.Widget _buildTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: _getTableHeaderStyle(),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // Build table data cell
  pw.Widget _buildTableCell(String text, {PdfColor? textColor}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: _fontService.getArabicTextStyle(
          fontSize: 11,
          color: textColor ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // ==================== EXCEL REPORTS ====================

  // Generate All Accounts Excel Report
  Future<File> generateAccountsExcelReport() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['تقرير الحسابات'];
      final accounts = await _storage.getAllAccounts();
      final currencySymbol = await _currencyService.getDefaultCurrencySymbol();
      final companyInfo = await _companyInfoService.getCompanyInfoForReports();

      // Set RTL direction
      sheet.isRTL = true;

      int currentRow = 0;

      // Add company header
      currentRow = await _addCompanyHeaderToExcel(
        sheet,
        companyInfo,
        currentRow,
      );

      // Add report title
      final titleCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
      );
      titleCell.value = TextCellValue('تقرير الحسابات الشامل');
      titleCell.cellStyle = CellStyle(
        fontSize: 16,
        bold: true,
        fontColorHex: ExcelColor.blue,
      );
      currentRow += 2;

      // Add summary
      final summaryCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
      );
      summaryCell.value = TextCellValue('عدد الحسابات: ${accounts.length}');
      summaryCell.cellStyle = CellStyle(fontSize: 12, bold: true);
      currentRow += 2;

      // Add table headers
      final headers = [
        'اسم الحساب',
        'الرصيد',
        'النوع',
        'عدد المعاملات',
        'تاريخ الإنشاء',
      ];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
        );
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          backgroundColorHex: ExcelColor.blue,
          fontColorHex: ExcelColor.white,
          bold: true,
          fontSize: 12,
        );
      }
      currentRow++;

      // Add account data
      for (final account in accounts) {
        final transactionCount = 0; // Will be calculated

        final rowData = [
          account.name,
          '${account.balance.toStringAsFixed(2)} $currencySymbol',
          account.typeInArabic,
          transactionCount.toString(),
          DateFormat('dd/MM/yyyy').format(account.createdAt ?? DateTime.now()),
        ];

        for (int i = 0; i < rowData.length; i++) {
          final cell = sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow),
          );
          cell.value = TextCellValue(rowData[i]);

          // Style balance cell based on value
          if (i == 1) {
            // Balance column
            cell.cellStyle = CellStyle(
              fontSize: 11,
              fontColorHex:
                  account.balance >= 0 ? ExcelColor.green : ExcelColor.red,
            );
          } else {
            cell.cellStyle = CellStyle(fontSize: 11);
          }
        }
        currentRow++;
      }

      // Save file
      final reportsDir = await _getReportsDirectory();
      final fileName =
          'تقرير_الحسابات_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx';
      final file = File('${reportsDir.path}/$fileName');
      final bytes = excel.save();
      if (bytes != null) {
        await file.writeAsBytes(bytes);
      }

      return file;
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير Excel: $e');
    }
  }

  // Add company header to Excel sheet
  Future<int> _addCompanyHeaderToExcel(
    Sheet sheet,
    CompanyInfo companyInfo,
    int startRow,
  ) async {
    int currentRow = startRow;

    // Company Name
    final companyNameCell = sheet.cell(
      CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
    );
    companyNameCell.value = TextCellValue(companyInfo.name);
    companyNameCell.cellStyle = CellStyle(
      fontSize: 18,
      bold: true,
      fontColorHex: ExcelColor.blue,
    );
    currentRow++;

    // Registration Number
    if (companyInfo.registrationNumber.isNotEmpty) {
      final regCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
      );
      regCell.value = TextCellValue(
        'رقم السجل التجاري: ${companyInfo.registrationNumber}',
      );
      regCell.cellStyle = CellStyle(fontSize: 11);
      currentRow++;
    }

    // Address
    if (companyInfo.address.isNotEmpty) {
      final addressCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
      );
      addressCell.value = TextCellValue(
        'العنوان: ${companyInfo.formattedAddress}',
      );
      addressCell.cellStyle = CellStyle(fontSize: 10);
      currentRow++;
    }

    // Contact Info
    if (companyInfo.contactInfo.isNotEmpty) {
      final contactCell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
      );
      contactCell.value = TextCellValue(companyInfo.contactInfo);
      contactCell.cellStyle = CellStyle(fontSize: 10);
      currentRow++;
    }

    // Report Date
    final dateCell = sheet.cell(
      CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow),
    );
    dateCell.value = TextCellValue(
      'تاريخ التقرير: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
    );
    dateCell.cellStyle = CellStyle(fontSize: 10);
    currentRow += 2; // Extra space

    return currentRow;
  }

  // Generate Account Statement PDF Report
  Future<File> generateAccountStatementPDF(Account account) async {
    try {
      // Load Arabic fonts and fallback fonts
      await _fontService.loadArabicFonts();
      await _fallbackFontService.initializeFallbackFonts();

      final pdf = pw.Document();
      final transactions = await _storage.getTransactionsByAccount(account.id);
      final currencySymbol = await _currencyService.getDefaultCurrencySymbol();

      // Calculate summary
      double totalCredit = 0;
      double totalDebit = 0;
      for (final transaction in transactions) {
        if (transaction.type.toString() == 'TransactionType.credit') {
          totalCredit += transaction.amount;
        } else {
          totalDebit += transaction.amount;
        }
      }

      // Determine colors based on account balance
      final isPositive = account.balance >= 0;
      final headerColor = isPositive ? _successColor : _errorColor;
      final backgroundColor = isPositive ? PdfColors.green50 : PdfColors.red50;

      // Build company header
      final companyHeader = await _buildCompanyHeader(
        reportTitle: 'كشف حساب',
        subtitle:
            'اسم الحساب: ${account.name} | الرصيد: ${account.balance.toStringAsFixed(2)} $currencySymbol',
        backgroundColor: backgroundColor,
        titleColor: headerColor,
      );

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return [
              // Company Header
              companyHeader,

              pw.SizedBox(height: 30),

              // Account Summary Section
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: backgroundColor,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: headerColor),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      'ملخص الحساب',
                      style: _fontService.getArabicHeaderStyle(
                        fontSize: 16,
                        color: headerColor,
                      ),
                    ),

                    pw.SizedBox(height: 12),

                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                      children: [
                        _buildSummaryCard(
                          'إجمالي الدائن',
                          '${totalCredit.toStringAsFixed(2)} $currencySymbol',
                          _successColor,
                        ),
                        _buildSummaryCard(
                          'إجمالي المدين',
                          '${totalDebit.toStringAsFixed(2)} $currencySymbol',
                          _errorColor,
                        ),
                        _buildSummaryCard(
                          'الرصيد النهائي',
                          '${account.balance.toStringAsFixed(2)} $currencySymbol',
                          headerColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Transactions Table
              if (transactions.isNotEmpty) ...[
                pw.Text(
                  'تفاصيل المعاملات',
                  style: _fontService.getArabicHeaderStyle(
                    fontSize: 18,
                    color: _darkBlue,
                  ),
                ),

                pw.SizedBox(height: 10),

                pw.Table(
                  border: pw.TableBorder.all(color: _greyDark),
                  columnWidths: {
                    0: const pw.FlexColumnWidth(2), // Date
                    1: const pw.FlexColumnWidth(3), // Description
                    2: const pw.FlexColumnWidth(1.5), // Type
                    3: const pw.FlexColumnWidth(2), // Amount
                    4: const pw.FlexColumnWidth(2), // Balance
                  },
                  children: [
                    // Header Row
                    pw.TableRow(
                      decoration: pw.BoxDecoration(color: _primaryColor),
                      children: [
                        _buildTableHeader('التاريخ'),
                        _buildTableHeader('الوصف'),
                        _buildTableHeader('النوع'),
                        _buildTableHeader('المبلغ'),
                        _buildTableHeader('الرصيد'),
                      ],
                    ),

                    // Data Rows
                    ...transactions.asMap().entries.map((entry) {
                      final index = entry.key;
                      final transaction = entry.value;
                      final isCredit =
                          transaction.type.toString() ==
                          'TransactionType.credit';

                      return pw.TableRow(
                        decoration: pw.BoxDecoration(
                          color: _getTableRowColor(index),
                        ),
                        children: [
                          _buildTableCell(
                            DateFormat('dd/MM/yyyy').format(transaction.date),
                          ),
                          _buildTableCell(
                            transaction.description.isEmpty
                                ? 'بدون وصف'
                                : transaction.description,
                          ),
                          _buildTableCell(isCredit ? 'دائن' : 'مدين'),
                          _buildTableCell(
                            '${transaction.amount.toStringAsFixed(2)} $currencySymbol',
                            textColor: isCredit ? _successColor : _errorColor,
                          ),
                          _buildTableCell(
                            '${account.balance.toStringAsFixed(2)} $currencySymbol',
                            textColor:
                                account.balance >= 0
                                    ? _successColor
                                    : _errorColor,
                          ),
                        ],
                      );
                    }),
                  ],
                ),
              ] else ...[
                pw.Center(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(20),
                    child: pw.Text(
                      'لا توجد معاملات مسجلة لهذا الحساب',
                      style: _fontService.getArabicTextStyle(
                        fontSize: 16,
                        color: _greyDark,
                      ),
                    ),
                  ),
                ),
              ],
            ];
          },
        ),
      );

      // Save file
      final reportsDir = await _getReportsDirectory();
      final fileName =
          'كشف_حساب_${account.name}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      final file = File('${reportsDir.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      return file;
    } catch (e) {
      throw Exception('فشل في إنشاء كشف الحساب: $e');
    }
  }

  // ==================== PUBLIC METHODS ====================

  // Generate all accounts reports (PDF + Excel)
  Future<List<File>> generateAllAccountsReports() async {
    try {
      final pdfFile = await generateAccountsPDFReport();
      final excelFile = await generateAccountsExcelReport();
      return [pdfFile, excelFile];
    } catch (e) {
      throw Exception('فشل في إنشاء التقارير: $e');
    }
  }

  // Generate account statement reports
  Future<List<File>> generateAccountStatementReports(Account account) async {
    try {
      final pdfFile = await generateAccountStatementPDF(account);
      return [pdfFile];
    } catch (e) {
      throw Exception('فشل في إنشاء كشف الحساب: $e');
    }
  }

  // Share report file
  Future<void> shareFile(File file, String title) async {
    try {
      await _shareFile(file, title);
    } catch (e) {
      throw Exception('فشل في مشاركة الملف: $e');
    }
  }

  // Get reports directory path
  Future<String> getReportsDirectoryPath() async {
    final dir = await _getReportsDirectory();
    return dir.path;
  }

  // Clean old reports (older than 30 days)
  Future<void> cleanOldReports() async {
    try {
      final reportsDir = await _getReportsDirectory();
      final files = reportsDir.listSync();
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      for (final file in files) {
        if (file is File) {
          final stat = file.statSync();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      // Ignore errors in cleanup
    }
  }

  // Get fonts status for debugging
  Future<Map<String, dynamic>> getFontsStatus() async {
    await _fontService.loadArabicFonts();
    await _fallbackFontService.initializeFallbackFonts();

    return {
      'arabicFonts': _fontService.getFontsStatus(),
      'fallbackFonts': _fallbackFontService.getFontsStatus(),
    };
  }

  // Print fonts status for debugging
  Future<void> printFontsStatus() async {
    final status = await getFontsStatus();
    print('=== تقرير حالة الخطوط ===');
    print('الخطوط العربية: ${status['arabicFonts']}');
    print('الخطوط الاحتياطية: ${status['fallbackFonts']}');
    print('========================');
  }
}
