import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/account.dart';
import 'add_account_screen.dart';
import 'account_details_screen.dart';
import 'add_amount_screen.dart';
import 'search_screen.dart';
import 'reports_screen.dart';
import 'all_accounts_screen.dart';
import '../widgets/app_drawer.dart';
import '../widgets/theme_toggle_button.dart';
import '../theme/modern_app_theme.dart';
import '../widgets/modern_ui_components.dart';
import '../services/responsive_service.dart';
import '../services/app_state_service.dart';
import '../services/custom_currency_service.dart';
import '../widgets/responsive_layout.dart';

/// الشاشة الرئيسية المحدثة بتصميم عصري ومبتكر
class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  final CustomCurrencyService _currencyService = CustomCurrencyService();
  String _currencySymbol = 'SAR';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateService>().loadAllData();
      _loadCurrencySymbol();
    });
  }

  Future<void> _loadCurrencySymbol() async {
    final symbol = await _currencyService.getDefaultCurrencySymbol();
    if (mounted) {
      setState(() {
        _currencySymbol = symbol;
      });
    }
  }

  Future<void> _addAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );
    if (result == true && mounted) {
      ModernUIComponents.showModernSnackBar(
        context: context,
        message: 'تم إضافة الحساب بنجاح',
        isSuccess: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateService>(
      builder: (context, appState, child) {
        final accounts = appState.accounts;
        final isLoading = appState.isLoading;
        final totalCredit = appState.totalPositiveBalance;
        final totalDebit = appState.totalNegativeBalance;

        return ResponsiveBuilder(
          builder: (context, deviceType) {
            return Scaffold(
              backgroundColor: ModernAppTheme.backgroundColor,
              drawer:
                  ResponsiveService.shouldUseDrawer(context)
                      ? const AppDrawer()
                      : null,
              extendBodyBehindAppBar: true,
              appBar: _buildModernAppBar(context, appState),
              body: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [ModernAppTheme.backgroundColor, Color(0xFFF1F5F9)],
                  ),
                ),
                child: SafeArea(
                  child:
                      isLoading
                          ? ModernUIComponents.modernLoadingIndicator(
                            message: 'جاري تحميل البيانات...',
                          )
                          : accounts.isEmpty
                          ? _buildModernWelcomeScreen()
                          : _buildModernMainContent(
                            accounts,
                            totalCredit,
                            totalDebit,
                            appState,
                          ),
                ),
              ),
              floatingActionButton: _buildModernFAB(),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.endFloat,
            );
          },
        );
      },
    );
  }

  /// بناء شريط التطبيق الحديث
  PreferredSizeWidget _buildModernAppBar(
    BuildContext context,
    AppStateService appState,
  ) {
    return ModernUIComponents.modernAppBar(
      title: 'دفتر الحسابات',
      gradientColors: const [
        ModernAppTheme.primaryColor,
        ModernAppTheme.primaryLight,
      ],
      leading:
          ResponsiveService.shouldUseDrawer(context)
              ? Builder(
                builder:
                    (context) => IconButton(
                      icon: const Icon(Icons.menu_rounded, color: Colors.white),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
              )
              : null,
      actions: [
        const ThemeToggleButton(),
        IconButton(
          icon: const Icon(Icons.refresh_rounded, color: Colors.white),
          onPressed: () => appState.refresh(),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  /// بناء زر الإضافة العائم الحديث
  Widget _buildModernFAB() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: ModernAppTheme.primaryGradient,
        boxShadow: [
          BoxShadow(
            color: ModernAppTheme.primaryColor.withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: _addAccount,
        backgroundColor: Colors.transparent,
        elevation: 0,
        icon: const Icon(Icons.add_rounded, color: Colors.white),
        label: const Text(
          'إضافة حساب',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }

  /// بناء شاشة الترحيب الحديثة
  Widget _buildModernWelcomeScreen() {
    return ModernUIComponents.modernEmptyState(
      icon: Icons.account_balance_wallet_rounded,
      title: 'مرحباً بك في دفتر الحسابات!',
      message: 'ابدأ رحلتك المالية بإضافة أول حساب\nوتتبع جميع معاملاتك بسهولة',
      actionText: 'إضافة حساب جديد',
      onAction: _addAccount,
    );
  }

  /// بناء المحتوى الرئيسي الحديث
  Widget _buildModernMainContent(
    List<Account> accounts,
    double totalCredit,
    double totalDebit,
    AppStateService appState,
  ) {
    return CustomScrollView(
      slivers: [
        // بطاقة الملخص المالي
        SliverToBoxAdapter(
          child: _buildModernFinancialSummary(totalCredit, totalDebit),
        ),
        // شريط الإجراءات السريعة
        SliverToBoxAdapter(child: _buildModernActionBar()),
        // عنوان الحسابات
        SliverToBoxAdapter(child: _buildAccountsHeader(accounts.length)),
        // شبكة الحسابات
        _buildModernAccountsGrid(accounts, appState),
      ],
    );
  }

  /// بناء الملخص المالي الحديث
  Widget _buildModernFinancialSummary(double totalCredit, double totalDebit) {
    final netBalance = totalCredit - totalDebit;

    return ModernUIComponents.modernCard(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      gradientColors: const [
        ModernAppTheme.primaryColor,
        ModernAppTheme.primaryLight,
        ModernAppTheme.accentColor,
      ],
      elevated: true,
      child: Column(
        children: [
          // الرصيد الصافي
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.account_balance_wallet_rounded,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: ModernAppTheme.spacingM),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الرصيد الصافي',
                    style: ModernAppTheme.bodyMedium.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${netBalance.toStringAsFixed(2)} $_currencySymbol',
                    style: ModernAppTheme.headingMedium.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: ModernAppTheme.spacingL),
          // إحصائيات مبسطة
          Row(
            children: [
              Expanded(
                child: _buildModernSummaryCard(
                  'لنا',
                  totalCredit,
                  Icons.trending_up_rounded,
                  ModernAppTheme.successColor,
                ),
              ),
              const SizedBox(width: ModernAppTheme.spacingM),
              Expanded(
                child: _buildModernSummaryCard(
                  'علينا',
                  totalDebit,
                  Icons.trending_down_rounded,
                  ModernAppTheme.errorColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة ملخص حديثة
  Widget _buildModernSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(ModernAppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(ModernAppTheme.radiusM),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: ModernAppTheme.iconSizeM),
              const SizedBox(width: ModernAppTheme.spacingS),
              Text(
                title,
                style: ModernAppTheme.labelMedium.copyWith(color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: ModernAppTheme.spacingS),
          Text(
            '${amount.toStringAsFixed(2)} $_currencySymbol',
            style: ModernAppTheme.bodyLarge.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الإجراءات السريعة الحديث
  Widget _buildModernActionBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: ModernAppTheme.spacingL),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // For very small screens, show only icons
          if (constraints.maxWidth < 300) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ModernUIComponents.modernButton(
                    text: '',
                    onPressed:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SearchScreen(),
                          ),
                        ),
                    icon: Icons.search_rounded,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: ModernAppTheme.spacingS),
                Expanded(
                  child: ModernUIComponents.modernButton(
                    text: '',
                    onPressed:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ReportsScreen(),
                          ),
                        ),
                    icon: Icons.analytics_rounded,
                    isPrimary: false,
                  ),
                ),
                const SizedBox(width: ModernAppTheme.spacingS),
                Expanded(
                  child: ModernUIComponents.modernButton(
                    text: '',
                    onPressed:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AllAccountsScreen(),
                          ),
                        ),
                    icon: Icons.list_rounded,
                    isPrimary: false,
                  ),
                ),
              ],
            );
          }

          // For normal screens, show text and icons
          return Row(
            children: [
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'البحث',
                  onPressed:
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SearchScreen(),
                        ),
                      ),
                  icon: Icons.search_rounded,
                  isPrimary: false,
                ),
              ),
              const SizedBox(width: ModernAppTheme.spacingS),
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'التقارير',
                  onPressed:
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ReportsScreen(),
                        ),
                      ),
                  icon: Icons.analytics_rounded,
                  isPrimary: false,
                ),
              ),
              const SizedBox(width: ModernAppTheme.spacingS),
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'الحسابات',
                  onPressed:
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AllAccountsScreen(),
                        ),
                      ),
                  icon: Icons.list_rounded,
                  isPrimary: false,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// بناء عنوان الحسابات
  Widget _buildAccountsHeader(int accountsCount) {
    return Container(
      margin: const EdgeInsets.all(ModernAppTheme.spacingL),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: ModernAppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.account_balance_rounded,
                    color: ModernAppTheme.primaryColor,
                    size: 18,
                  ),
                ),
                const SizedBox(width: ModernAppTheme.spacingS),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحسابات',
                        style: ModernAppTheme.bodyLarge.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '$accountsCount حساب',
                        style: ModernAppTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: ModernAppTheme.successLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: ModernAppTheme.successColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_rounded,
                  color: ModernAppTheme.successColor,
                  size: 12,
                ),
                const SizedBox(width: 2),
                Text(
                  'محدث',
                  style: TextStyle(
                    color: ModernAppTheme.successColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شبكة الحسابات الحديثة
  Widget _buildModernAccountsGrid(
    List<Account> accounts,
    AppStateService appState,
  ) {
    return SliverPadding(
      padding: const EdgeInsets.fromLTRB(
        ModernAppTheme.spacingL,
        0,
        ModernAppTheme.spacingL,
        100, // مساحة إضافية للـ FAB
      ),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 2,
          childAspectRatio: MediaQuery.of(context).size.width > 600 ? 1.1 : 1.0,
          crossAxisSpacing: ModernAppTheme.spacingM,
          mainAxisSpacing: ModernAppTheme.spacingM,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final account = accounts[index];
          return _buildModernAccountCard(account, appState);
        }, childCount: accounts.length),
      ),
    );
  }

  /// بناء كارت حساب حديث
  Widget _buildModernAccountCard(Account account, AppStateService appState) {
    final isPositive = account.isPositive;
    final primaryColor =
        isPositive ? ModernAppTheme.successColor : ModernAppTheme.errorColor;
    final lightColor =
        isPositive ? ModernAppTheme.successLight : ModernAppTheme.errorLight;

    return ModernUIComponents.modernCard(
      margin: EdgeInsets.zero,
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AccountDetailsScreen(account: account),
          ),
        );
        if (result == true) {
          appState.refresh();
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // رأس الكارت
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  isPositive
                      ? Icons.trending_up_rounded
                      : Icons.trending_down_rounded,
                  color: primaryColor,
                  size: 18,
                ),
              ),
              SizedBox(
                width: 28,
                height: 28,
                child: PopupMenuButton<String>(
                  padding: EdgeInsets.zero,
                  iconSize: 16,
                  onSelected:
                      (value) => _handleAccountAction(value, account, appState),
                  itemBuilder:
                      (context) => [
                        PopupMenuItem(
                          value: 'add_transaction',
                          child: SizedBox(
                            width: 140,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.add_rounded, size: 14),
                                SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    'إضافة معاملة',
                                    style: TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        PopupMenuItem(
                          value: 'edit',
                          child: SizedBox(
                            width: 140,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.edit_rounded, size: 14),
                                SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    'تعديل',
                                    style: TextStyle(fontSize: 12),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: SizedBox(
                            width: 140,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.delete_rounded,
                                  size: 14,
                                  color: Colors.red,
                                ),
                                SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    'حذف',
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                  child: Icon(
                    Icons.more_vert_rounded,
                    color: ModernAppTheme.textSecondary,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          // اسم الحساب
          Text(
            account.name,
            style: ModernAppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 6),
          // نوع الحساب
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
            decoration: BoxDecoration(
              color: lightColor,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              isPositive ? 'دائن' : 'مدين',
              style: TextStyle(
                fontSize: 12,
                color: primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 6),
          // الرصيد
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: lightColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'الرصيد',
                    style: TextStyle(
                      fontSize: 10,
                      color: primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        '${account.balance.toStringAsFixed(2)} $_currencySymbol',
                        style: TextStyle(
                          fontSize: 14,
                          color: primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// معالجة إجراءات الحساب
  void _handleAccountAction(
    String action,
    Account account,
    AppStateService appState,
  ) {
    switch (action) {
      case 'add_transaction':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AddAmountScreen(account: account),
          ),
        ).then((result) {
          if (result == true) {
            appState.refresh();
          }
        });
        break;
      case 'edit':
        ModernUIComponents.showModernSnackBar(
          context: context,
          message: 'ميزة تعديل الحساب قيد التطوير',
        );
        break;
      case 'delete':
        _showDeleteConfirmation(account, appState);
        break;
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Account account, AppStateService appState) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await appState.deleteAccount(account.id);
                  if (mounted) {
                    ModernUIComponents.showModernSnackBar(
                      context: context,
                      message: 'تم حذف الحساب بنجاح',
                      isSuccess: true,
                    );
                  }
                },
                child: const Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }
}
