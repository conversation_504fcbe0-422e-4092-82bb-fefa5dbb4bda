class CompanyInfo {
  final String id;
  final String name;
  final String registrationNumber;
  final String? logoPath;
  final String address;
  final String? phoneNumber;
  final String? email;
  final DateTime createdAt;
  final DateTime updatedAt;

  CompanyInfo({
    required this.id,
    required this.name,
    required this.registrationNumber,
    this.logoPath,
    required this.address,
    this.phoneNumber,
    this.email,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'registrationNumber': registrationNumber,
      'logoPath': logoPath,
      'address': address,
      'phoneNumber': phoneNumber,
      'email': email,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  // Create from Map (database)
  factory CompanyInfo.fromMap(Map<String, dynamic> map) {
    return CompanyInfo(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      registrationNumber: map['registrationNumber'] ?? '',
      logoPath: map['logoPath'],
      address: map['address'] ?? '',
      phoneNumber: map['phoneNumber'],
      email: map['email'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
    );
  }

  // Copy with method for updates
  CompanyInfo copyWith({
    String? id,
    String? name,
    String? registrationNumber,
    String? logoPath,
    String? address,
    String? phoneNumber,
    String? email,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      logoPath: logoPath ?? this.logoPath,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanyInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CompanyInfo(id: $id, name: $name, registrationNumber: $registrationNumber)';
  }

  // Validation methods
  bool get isValid {
    return id.isNotEmpty && name.isNotEmpty && address.isNotEmpty;
  }

  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'اسم الشركة مطلوب';
    }
    if (name.trim().length < 2) {
      return 'اسم الشركة يجب أن يكون أكثر من حرف واحد';
    }
    if (name.trim().length > 100) {
      return 'اسم الشركة طويل جداً';
    }
    return null;
  }

  static String? validateRegistrationNumber(String? number) {
    if (number == null || number.trim().isEmpty) {
      return 'رقم السجل التجاري مطلوب';
    }
    if (number.trim().length < 3) {
      return 'رقم السجل التجاري قصير جداً';
    }
    if (number.trim().length > 50) {
      return 'رقم السجل التجاري طويل جداً';
    }
    return null;
  }

  static String? validateAddress(String? address) {
    if (address == null || address.trim().isEmpty) {
      return 'عنوان الشركة مطلوب';
    }
    if (address.trim().length < 5) {
      return 'عنوان الشركة قصير جداً';
    }
    if (address.trim().length > 500) {
      return 'عنوان الشركة طويل جداً';
    }
    return null;
  }

  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return null; // Phone is optional
    }
    // Basic phone validation
    final phoneRegex = RegExp(r'^[\+]?[0-9\-\(\)\s]{7,20}$');
    if (!phoneRegex.hasMatch(phone.trim())) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  static String? validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return null; // Email is optional
    }
    // Basic email validation
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email.trim())) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }

  // Generate unique ID
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Default company info for initial setup
  static CompanyInfo getDefault() {
    final now = DateTime.now();
    return CompanyInfo(
      id: generateId(),
      name: 'اسم الشركة',
      registrationNumber: '1234567890',
      address: 'عنوان الشركة',
      createdAt: now,
      updatedAt: now,
    );
  }

  // Check if company has logo
  bool get hasLogo => logoPath != null && logoPath!.isNotEmpty;

  // Get formatted address for display
  String get formattedAddress {
    return address.replaceAll('\n', ' - ');
  }

  // Get contact info string
  String get contactInfo {
    final contacts = <String>[];
    if (phoneNumber != null && phoneNumber!.isNotEmpty) {
      contacts.add('هاتف: $phoneNumber');
    }
    if (email != null && email!.isNotEmpty) {
      contacts.add('بريد: $email');
    }
    return contacts.join(' | ');
  }
}
