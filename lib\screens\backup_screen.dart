import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../database/simple_storage.dart';

class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen> {
  final SimpleStorage _storage = SimpleStorage();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: const Color(0xFF4A5FC7),
        elevation: 0,
        title: const Text(
          'حفظ نسخة احتياطية',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Info card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue.shade600,
                    size: 48,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'النسخة الاحتياطية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'احفظ نسخة احتياطية من بياناتك لضمان عدم فقدانها',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14, color: Colors.blue.shade700),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Backup options
            _buildBackupOption(
              icon: Icons.cloud_upload,
              title: 'رفع إلى جوجل درايف',
              subtitle: 'احفظ النسخة الاحتياطية في جوجل درايف',
              onTap: _backupToGoogleDrive,
            ),

            const SizedBox(height: 12),

            _buildBackupOption(
              icon: Icons.share,
              title: 'مشاركة النسخة الاحتياطية',
              subtitle: 'شارك ملف النسخة الاحتياطية',
              onTap: _shareBackup,
            ),

            const SizedBox(height: 12),

            _buildBackupOption(
              icon: Icons.download,
              title: 'تحميل النسخة الاحتياطية',
              subtitle: 'احفظ النسخة الاحتياطية على الجهاز',
              onTap: _downloadBackup,
            ),

            const Spacer(),

            // Last backup info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Icon(Icons.schedule, color: Colors.grey, size: 24),
                  const SizedBox(height: 8),
                  const Text(
                    'آخر نسخة احتياطية',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'لم يتم إنشاء نسخة احتياطية بعد',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFF4A5FC7).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: const Color(0xFF4A5FC7), size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
        ),
        trailing:
            _isLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey,
                  size: 16,
                ),
        onTap: _isLoading ? null : onTap,
      ),
    );
  }

  Future<void> _backupToGoogleDrive() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement Google Drive backup
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('سيتم تطبيق رفع جوجل درايف قريباً'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في الرفع: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _shareBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Create backup data
      final backupData = await _createBackupData();

      // Share the backup data
      await Share.share(backupData, subject: 'نسخة احتياطية من تطبيق الحسابات');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء النسخة الاحتياطية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إنشاء النسخة الاحتياطية: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Create backup data
      final backupData = await _createBackupData();

      // For web, we can trigger a download
      // For mobile, we can save to downloads folder

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحميل النسخة الاحتياطية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل النسخة الاحتياطية: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<String> _createBackupData() async {
    // Create backup using the database method
    final backup = await _storage.createBackup();
    return backup.toString();
  }
}
