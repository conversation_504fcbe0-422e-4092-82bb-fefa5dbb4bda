import 'package:flutter/material.dart';
import '../services/settings_manager.dart';
import '../models/app_settings.dart';
import '../database/database_helper.dart';

class SettingsDialogs {
  static void showTimePickerDialog(
    BuildContext context,
    AppSettings settings,
    Function(AppSettings) onSettingsUpdate,
  ) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      final timeString =
          '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      onSettingsUpdate(settings.copyWith(backupTime: timeString));
    }
  }

  static void showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('استيراد إعدادات'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('يمكنك استيراد الإعدادات من ملف JSON:'),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'الصق محتوى ملف JSON هنا',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 5,
                  onChanged: (value) {
                    // Store the JSON content for import
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('سيتم تطبيق هذه الميزة قريباً'),
                    ),
                  );
                },
                child: const Text('استيراد'),
              ),
            ],
          ),
    );
  }

  static void showExportDialog(
    BuildContext context,
    SettingsManager settingsManager,
  ) {
    final settingsJson = settingsManager.exportSettings();
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تصدير إعدادات'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('تم تصدير الإعدادات بنجاح:'),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SelectableText(
                    settingsJson,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'انسخ النص أعلاه واحفظه في ملف لاستيراده لاحقاً',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  static void showPasswordSetupDialog(
    BuildContext context,
    AppSettings settings,
    SettingsManager settingsManager,
    Function(AppSettings) onSettingsUpdate,
  ) {
    final TextEditingController passwordController = TextEditingController();
    final TextEditingController confirmController = TextEditingController();
    bool obscurePassword = true;
    bool obscureConfirm = true;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('إعداد كلمة المرور'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل وتتضمن أحرف كبيرة وصغيرة وأرقام',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: passwordController,
                        obscureText: obscurePassword,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              obscurePassword
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                obscurePassword = !obscurePassword;
                              });
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: confirmController,
                        obscureText: obscureConfirm,
                        decoration: InputDecoration(
                          labelText: 'تأكيد كلمة المرور',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              obscureConfirm
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                obscureConfirm = !obscureConfirm;
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () {
                        if (passwordController.text == confirmController.text) {
                          if (settingsManager.isPasswordStrong(
                            passwordController.text,
                          )) {
                            final hash = settingsManager.hashPassword(
                              passwordController.text,
                            );
                            onSettingsUpdate(
                              settings.copyWith(
                                passwordEnabled: true,
                                passwordHash: hash,
                              ),
                            );
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم إعداد كلمة المرور بنجاح'),
                              ),
                            );
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل',
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('كلمات المرور غير متطابقة'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      child: const Text('حفظ'),
                    ),
                  ],
                ),
          ),
    );
  }

  static void showPasswordChangeDialog(
    BuildContext context,
    AppSettings settings,
    SettingsManager settingsManager,
    Function(AppSettings) onSettingsUpdate,
  ) {
    final TextEditingController currentController = TextEditingController();
    final TextEditingController newController = TextEditingController();
    final TextEditingController confirmController = TextEditingController();
    bool obscureCurrent = true;
    bool obscureNew = true;
    bool obscureConfirm = true;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('تغيير كلمة المرور'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: currentController,
                        obscureText: obscureCurrent,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور الحالية',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              obscureCurrent
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                obscureCurrent = !obscureCurrent;
                              });
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: newController,
                        obscureText: obscureNew,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور الجديدة',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              obscureNew
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                obscureNew = !obscureNew;
                              });
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: confirmController,
                        obscureText: obscureConfirm,
                        decoration: InputDecoration(
                          labelText: 'تأكيد كلمة المرور الجديدة',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              obscureConfirm
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                obscureConfirm = !obscureConfirm;
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                    TextButton(
                      onPressed: () {
                        if (settingsManager.verifyPassword(
                          currentController.text,
                          settings.passwordHash,
                        )) {
                          if (newController.text == confirmController.text) {
                            if (settingsManager.isPasswordStrong(
                              newController.text,
                            )) {
                              final hash = settingsManager.hashPassword(
                                newController.text,
                              );
                              onSettingsUpdate(
                                settings.copyWith(passwordHash: hash),
                              );
                              Navigator.pop(context);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم تغيير كلمة المرور بنجاح'),
                                ),
                              );
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('كلمة المرور الجديدة ضعيفة'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'كلمات المرور الجديدة غير متطابقة',
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('كلمة المرور الحالية غير صحيحة'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      child: const Text('حفظ'),
                    ),
                  ],
                ),
          ),
    );
  }

  static void showClearDataDialog(
    BuildContext context,
    DatabaseHelper storage,
    VoidCallback onDataCleared,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مسح البيانات'),
            content: const Text(
              'تحذير: سيتم حذف جميع البيانات نهائياً بما في ذلك:\n'
              '• جميع الحسابات\n'
              '• جميع المعاملات\n'
              '• التقارير والإحصائيات\n\n'
              'هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  try {
                    await storage.clearAllData();
                    onDataCleared();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم مسح جميع البيانات بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في مسح البيانات: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف نهائياً'),
              ),
            ],
          ),
    );
  }

  static void showResetSettingsDialog(
    BuildContext context,
    SettingsManager settingsManager,
    VoidCallback onSettingsReset,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعادة تعيين الإعدادات'),
            content: const Text(
              'هل تريد إعادة جميع الإعدادات للوضع الافتراضي؟\n\n'
              'سيتم إعادة تعيين:\n'
              '• الإعدادات العامة\n'
              '• الإعدادات المالية\n'
              '• إعدادات النسخ الاحتياطي\n'
              '• إعدادات الأمان\n'
              '• إعدادات واجهة المستخدم',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await settingsManager.resetToDefaults();
                  onSettingsReset();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إعادة تعيين الإعدادات بنجاح'),
                    ),
                  );
                },
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
    );
  }
}
