# تشغيل سريع للتطبيق

## خطوات التشغيل

1. **افتح Terminal أو Command Prompt**
2. **انتقل إلى مجلد المشروع:**
   ```bash
   cd acount
   ```

3. **تحديث التبعيات:**
   ```bash
   flutter pub get
   ```

4. **تشغيل التطبيق:**
   ```bash
   flutter run
   ```

## إذا واجهت مشاكل

### مشكلة: Flutter غير مثبت
```bash
# تحقق من تثبيت Flutter
flutter --version

# إذا لم يكن مثبت، قم بتحميله من:
# https://flutter.dev/docs/get-started/install
```

### مشكلة: لا يوجد جهاز متصل
```bash
# تحقق من الأجهزة المتاحة
flutter devices

# لتشغيل على متصفح الويب
flutter run -d chrome

# لتشغيل على محاكي Android
flutter emulators
flutter emulators --launch <emulator_id>
```

### مشكلة: أخطاء في التبعيات
```bash
# تنظيف المشروع
flutter clean

# إعادة تحديث التبعيات
flutter pub get

# إعادة التشغيل
flutter run
```

## اختصارات مفيدة أثناء التشغيل

- `r` - إعادة تحميل سريع (Hot Reload)
- `R` - إعادة تشغيل كامل (Hot Restart)
- `q` - إيقاف التطبيق
- `h` - عرض المساعدة

## المميزات الجديدة 🎉

### قاعدة البيانات المحلية
- جميع البيانات محفوظة محلياً
- لا حاجة لاتصال بالإنترنت
- بيانات تجريبية جاهزة للاختبار

### الوظائف المتقدمة
- **البحث**: اضغط على أيقونة البحث في الشريط العلوي
- **التقارير**: اضغط على أيقونة التقارير لعرض الإحصائيات
- **تفاصيل الحساب**: اضغط على أي حساب لعرض المعاملات
- **إضافة معاملة**: اضغط على زر + في تفاصيل الحساب
- **المشاركة**: شارك التقارير والبيانات

## معلومات إضافية

- التطبيق يدعم Android و iOS و Web
- يمكن تشغيله على المحاكي أو الجهاز الحقيقي
- يدعم اللغة العربية والتخطيط من اليمين إلى اليسار
- قاعدة بيانات SQLite محلية
- رسوم بيانية تفاعلية
