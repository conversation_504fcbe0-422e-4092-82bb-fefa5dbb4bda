import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../database/database_helper.dart';
import '../models/transaction.dart';
import '../services/custom_currency_service.dart';

class DetailedReportsScreen extends StatefulWidget {
  const DetailedReportsScreen({super.key});

  @override
  State<DetailedReportsScreen> createState() => _DetailedReportsScreenState();
}

class _DetailedReportsScreenState extends State<DetailedReportsScreen> {
  final DatabaseHelper _storage = DatabaseHelper();
  final CustomCurrencyService _currencyService = CustomCurrencyService();
  List<Transaction> _transactions = [];
  bool _isLoading = true;
  String _selectedFilter = 'الكل';
  String _currencySymbol = 'SAR';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load default currency symbol
      final currencySymbol = await _currencyService.getDefaultCurrencySymbol();

      List<Transaction> transactions;

      if (_startDate != null && _endDate != null) {
        transactions = await _storage.getTransactionsByDateRange(
          _startDate!,
          _endDate!,
        );
      } else {
        transactions = await _storage.getAllTransactions();
      }

      if (_selectedFilter != 'الكل') {
        transactions =
            transactions.where((t) {
              if (_selectedFilter == 'دائن') {
                return t.type == TransactionType.credit;
              }
              if (_selectedFilter == 'مدين') {
                return t.type == TransactionType.debit;
              }
              return true;
            }).toList();
      }

      setState(() {
        _currencySymbol = currencySymbol;
        _transactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('تقرير تفاصيل كل المبالغ'),
        backgroundColor: const Color(0xFF4A5FC7),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Card
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: _buildSummary(),
          ),

          // Transactions List
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _transactions.isEmpty
                    ? Center(
                      child: Text(
                        'لا توجد معاملات',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = _transactions[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor:
                                  transaction.type == TransactionType.credit
                                      ? Colors.green
                                      : Colors.red,
                              child: Icon(
                                transaction.type == TransactionType.credit
                                    ? Icons.add
                                    : Icons.remove,
                                color: Colors.white,
                              ),
                            ),
                            title: Text(transaction.description),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  DateFormat(
                                    'dd/MM/yyyy - HH:mm',
                                  ).format(transaction.date),
                                  style: const TextStyle(fontSize: 12),
                                ),
                                if (transaction.notes != null &&
                                    transaction.notes!.isNotEmpty)
                                  Text(
                                    transaction.notes!,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                              ],
                            ),
                            trailing: Text(
                              '${transaction.amount.toStringAsFixed(0)} $_currencySymbol',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color:
                                    transaction.type == TransactionType.credit
                                        ? Colors.green
                                        : Colors.red,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummary() {
    double totalCredit = 0;
    double totalDebit = 0;

    for (var transaction in _transactions) {
      if (transaction.type == TransactionType.credit) {
        totalCredit += transaction.amount;
      } else {
        totalDebit += transaction.amount;
      }
    }

    final balance = totalCredit - totalDebit;

    return Column(
      children: [
        Text(
          'ملخص المعاملات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildSummaryItem('إجمالي الدائن', totalCredit, Colors.green),
            _buildSummaryItem('إجمالي المدين', totalDebit, Colors.red),
            _buildSummaryItem(
              'الرصيد',
              balance,
              balance >= 0 ? Colors.green : Colors.red,
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          'عدد المعاملات: ${_transactions.length}',
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(String label, double amount, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(0)} $_currencySymbol',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: Theme.of(context).cardColor,
            title: Text(
              'تصفية المعاملات',
              style: TextStyle(
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Filter by type
                Text(
                  'نوع المعاملة:',
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
                DropdownButton<String>(
                  value: _selectedFilter,
                  isExpanded: true,
                  items:
                      ['الكل', 'دائن', 'مدين'].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),

                const SizedBox(height: 16),

                // Date range
                Text(
                  'فترة زمنية:',
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _startDate = date;
                            });
                          }
                        },
                        child: Text(
                          _startDate != null
                              ? DateFormat('dd/MM/yyyy').format(_startDate!)
                              : 'من تاريخ',
                        ),
                      ),
                    ),
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _endDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _endDate = date;
                            });
                          }
                        },
                        child: Text(
                          _endDate != null
                              ? DateFormat('dd/MM/yyyy').format(_endDate!)
                              : 'إلى تاريخ',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedFilter = 'الكل';
                    _startDate = null;
                    _endDate = null;
                  });
                  Navigator.pop(context);
                  _loadTransactions();
                },
                child: const Text('إعادة تعيين'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _loadTransactions();
                },
                child: const Text('تطبيق'),
              ),
            ],
          ),
    );
  }
}
