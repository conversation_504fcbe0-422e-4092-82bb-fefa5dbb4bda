import 'package:flutter/material.dart';
import '../screens/reports_screen.dart';
import '../screens/backup_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/about_screen.dart';
import '../screens/detailed_reports_screen.dart';
import '../screens/monthly_reports_screen.dart';
import '../screens/category_reports_screen.dart';

import '../screens/currency_management_screen.dart';
import '../screens/biometric_settings_screen.dart';
import '../screens/theme_settings_screen.dart';
import '../screens/login_screen.dart';
import '../services/auth_service.dart';
import '../database/database_helper.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();
    final currentUser = authService.currentUser;

    return Drawer(
      backgroundColor: Colors.white,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // Header with user info
          Container(
            height: 140,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF4A5FC7), Color(0xFF667eea)],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: Colors.white,
                          child:
                              currentUser != null
                                  ? Text(
                                    currentUser.fullName.isNotEmpty
                                        ? currentUser.fullName[0].toUpperCase()
                                        : currentUser.username[0].toUpperCase(),
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF4A5FC7),
                                    ),
                                  )
                                  : const Icon(
                                    Icons.account_balance_wallet,
                                    size: 24,
                                    color: Color(0xFF4A5FC7),
                                  ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                currentUser?.fullName ?? 'دفتر الحسابات',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                currentUser != null
                                    ? '@${currentUser.username}'
                                    : 'إدارة حساباتك بسهولة',
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 12,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        if (currentUser?.biometricEnabled == true)
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.fingerprint_rounded,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // إضافة مبلغ
          _buildDrawerItem(
            context,
            icon: Icons.add,
            iconColor: Colors.green,
            title: 'إضافة مبلغ',
            onTap: () {
              Navigator.pop(context);
              // Navigate to add amount for first account or show account selection
              _showAddAmountDialog(context);
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // تقرير- إجمالي المبالغ
          _buildDrawerItem(
            context,
            icon: Icons.calendar_today,
            iconColor: Colors.grey,
            title: 'تقرير- إجمالي المبالغ',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ReportsScreen()),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // تقرير- تفاصيل كل المبالغ
          _buildDrawerItem(
            context,
            icon: Icons.calendar_today,
            iconColor: Colors.grey,
            title: 'تقرير- تفاصيل كل المبالغ',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DetailedReportsScreen(),
                ),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // تقرير- إجمالي المبالغ شهريا
          _buildDrawerItem(
            context,
            icon: Icons.calendar_today,
            iconColor: Colors.grey,
            title: 'تقرير- إجمالي المبالغ شهريا',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MonthlyReportsScreen(),
                ),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // تقرير إجمالي التصنيفات
          _buildDrawerItem(
            context,
            icon: Icons.calendar_today,
            iconColor: Colors.grey,
            title: 'تقرير إجمالي التصنيفات',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CategoryReportsScreen(),
                ),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // حفظ نسخة احتياطية
          _buildDrawerItem(
            context,
            icon: Icons.cloud_upload,
            iconColor: Colors.grey,
            title: 'حفظ نسخة احتياطية',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const BackupScreen()),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // استرجاع قاعدة البيانات
          _buildDrawerItem(
            context,
            icon: Icons.cloud_download,
            iconColor: Colors.grey,
            title: 'استرجاع قاعدة البيانات',
            onTap: () {
              Navigator.pop(context);
              _showRestoreDialog(context);
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // جوجل درايف
          _buildDrawerItem(
            context,
            icon: Icons.cloud,
            iconColor: Colors.blue,
            title: 'جوجل درايف',
            onTap: () {
              Navigator.pop(context);
              _launchGoogleDrive();
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // إدارة العملات
          _buildDrawerItem(
            context,
            icon: Icons.currency_exchange,
            iconColor: Colors.amber,
            title: 'إدارة العملات',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CurrencyManagementScreen(),
                ),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // إعدادات
          _buildDrawerItem(
            context,
            icon: Icons.settings,
            iconColor: Colors.grey,
            title: 'إعدادات',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // إعدادات البصمة
          _buildDrawerItem(
            context,
            icon: Icons.fingerprint_rounded,
            iconColor: Colors.indigo,
            title: 'إعدادات البصمة',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BiometricSettingsScreen(),
                ),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // إعدادات المظهر
          _buildDrawerItem(
            context,
            icon: Icons.palette_rounded,
            iconColor: Colors.purple,
            title: 'إعدادات المظهر',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ThemeSettingsScreen(),
                ),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // للتواصل والدعم
          _buildDrawerItem(
            context,
            icon: Icons.support_agent,
            iconColor: Colors.grey,
            title: 'للتواصل والدعم',
            onTap: () {
              Navigator.pop(context);
              _showContactDialog(context);
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // حول البرنامج
          _buildDrawerItem(
            context,
            icon: Icons.help_outline,
            iconColor: Colors.grey,
            title: 'حول البرنامج',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const AboutScreen()),
              );
            },
          ),

          const Divider(height: 1, color: Colors.grey),

          // تسجيل الخروج
          _buildDrawerItem(
            context,
            icon: Icons.logout_rounded,
            iconColor: Colors.red,
            title: 'تسجيل الخروج',
            onTap: () {
              _showLogoutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: iconColor, size: 20),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      dense: true,
      minLeadingWidth: 32,
    );
  }

  void _showAddAmountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة مبلغ'),
            content: const Text('يرجى اختيار حساب من الشاشة الرئيسية أولاً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  void _showRestoreDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('استرجاع قاعدة البيانات'),
            content: const Text(
              'هل تريد استرجاع نسخة احتياطية من قاعدة البيانات؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _performRestore(context);
                },
                child: const Text('استرجاع'),
              ),
            ],
          ),
    );
  }

  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('للتواصل والدعم'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.email, color: Colors.blue),
                  title: const Text('البريد الإلكتروني'),
                  subtitle: const Text('<EMAIL>'),
                  onTap: () => _launchEmail(),
                ),
                ListTile(
                  leading: const Icon(Icons.phone, color: Colors.green),
                  title: const Text('واتساب'),
                  subtitle: const Text('+966 50 123 4567'),
                  onTap: () => _launchWhatsApp(),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل تريد تسجيل الخروج من حسابك؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _performLogout(context);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
    );
  }

  Future<void> _performLogout(BuildContext context) async {
    try {
      final authService = AuthService();
      await authService.logout();

      if (context.mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الخروج: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _performRestore(BuildContext context) async {
    try {
      final storage = DatabaseHelper();

      // Clear current data
      await storage.clearAllData();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم استرجاع البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في استرجاع البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _launchGoogleDrive() {
    // TODO: Implement Google Drive integration
  }

  void _launchEmail() {
    // TODO: Implement email launcher
  }

  void _launchWhatsApp() {
    // TODO: Implement WhatsApp launcher
  }
}
