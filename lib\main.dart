import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'services/theme_service.dart';
import 'services/theme_manager.dart';
import 'services/language_service.dart';
import 'services/notification_service.dart';
import 'services/settings_manager.dart';
import 'services/app_state_service.dart';

import 'theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set system UI overlay style for modern look
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Initialize services
  final settingsManager = SettingsManager();
  final themeService = ThemeService();
  final languageService = LanguageService();
  final notificationService = NotificationService();
  final appStateService = AppStateService();

  await settingsManager.init();

  // Initialize other services with settings
  themeService.initialize(settingsManager.settings);
  languageService.initialize(settingsManager.settings);
  await notificationService.initialize(settingsManager.settings);

  // Initialize theme manager
  final themeManager = ThemeManager();
  await themeManager.loadTheme();

  // Load all data from database
  await appStateService.loadAllData();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: themeService),
        ChangeNotifierProvider.value(value: themeManager),
        ChangeNotifierProvider.value(value: languageService),
        ChangeNotifierProvider.value(value: appStateService),
        Provider.value(value: settingsManager),
        Provider.value(value: notificationService),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // حفظ البيانات عند إيقاف التطبيق أو إخفاؤه
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      _saveDataOnExit();
    }
  }

  Future<void> _saveDataOnExit() async {
    try {
      // التأكد من حفظ جميع البيانات فوراً
      final appStateService = AppStateService();
      await appStateService.saveAllDataImmediately();
      debugPrint('تم حفظ البيانات بنجاح عند الخروج');
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات عند الخروج: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<ThemeService, ThemeManager, LanguageService>(
      builder: (context, themeService, themeManager, languageService, child) {
        return MaterialApp(
          title: languageService.getString('app_name'),
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeManager.themeMode,
          locale: languageService.currentLocale,
          supportedLocales: const [
            Locale('ar', 'SA'),
            Locale('en', 'US'),
            Locale('fr', 'FR'),
            Locale('es', 'ES'),
          ],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          builder: (context, child) {
            return Directionality(
              textDirection: languageService.textDirection,
              child: child!,
            );
          },
          home: const SplashScreen(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}
