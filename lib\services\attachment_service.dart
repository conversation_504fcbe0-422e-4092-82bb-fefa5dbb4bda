import 'dart:io';

import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path/path.dart' as path;

class AttachmentService {
  static final AttachmentService _instance = AttachmentService._internal();
  factory AttachmentService() => _instance;
  AttachmentService._internal();

  final ImagePicker _imagePicker = ImagePicker();

  // طلب الأذونات المطلوبة
  Future<bool> requestPermissions() async {
    if (Platform.isAndroid) {
      try {
        debugPrint('🔐 طلب الأذونات...');

        // طلب إذن الكاميرا أولاً
        var cameraStatus = await Permission.camera.request();
        debugPrint('📷 حالة الكاميرا: ${cameraStatus.toString()}');

        // طلب إذن التخزين
        var storageStatus = await Permission.storage.request();
        debugPrint('💾 حالة التخزين: ${storageStatus.toString()}');

        // للأندرويد 13+ طلب إذن الصور
        var photosStatus = await Permission.photos.request();
        debugPrint('🖼️ حالة الصور: ${photosStatus.toString()}');

        bool cameraGranted = cameraStatus.isGranted;
        bool storageGranted = storageStatus.isGranted;
        bool photosGranted = photosStatus.isGranted;

        debugPrint('🔐 ملخص الأذونات:');
        debugPrint('📷 الكاميرا: ${cameraGranted ? "مسموح" : "مرفوض"}');
        debugPrint('💾 التخزين: ${storageGranted ? "مسموح" : "مرفوض"}');
        debugPrint('🖼️ الصور: ${photosGranted ? "مسموح" : "مرفوض"}');

        // نحتاج الكاميرا وأحد أذونات التخزين على الأقل
        bool hasStoragePermission = storageGranted || photosGranted;
        bool allPermissionsGranted = cameraGranted && hasStoragePermission;

        debugPrint('✅ جميع الأذونات مسموحة: $allPermissionsGranted');

        return allPermissionsGranted;
      } catch (e) {
        debugPrint('💥 خطأ في طلب الأذونات: $e');
        return false;
      }
    }
    return true; // iOS يطلب الأذونات تلقائياً
  }

  // اختيار صورة من المعرض
  Future<AttachmentResult?> pickImageFromGallery() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('الأذونات مطلوبة للوصول للمعرض');
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        final savedPath = await _saveAttachment(image.path, image.name);
        return AttachmentResult(
          path: savedPath,
          name: image.name,
          type: 'image',
          size: await image.length(),
        );
      }
    } catch (e) {
      debugPrint('خطأ في اختيار الصورة: $e');
      rethrow;
    }
    return null;
  }

  // التقاط صورة بالكاميرا
  Future<AttachmentResult?> captureImage() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('الأذونات مطلوبة للوصول للكاميرا');
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        final savedPath = await _saveAttachment(image.path, image.name);
        return AttachmentResult(
          path: savedPath,
          name: image.name,
          type: 'image',
          size: await image.length(),
        );
      }
    } catch (e) {
      debugPrint('خطأ في التقاط الصورة: $e');
      rethrow;
    }
    return null;
  }

  // اختيار مستند
  Future<AttachmentResult?> pickDocument() async {
    try {
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('الأذونات مطلوبة للوصول للملفات');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        final savedPath = await _saveAttachment(file.path!, file.name);
        return AttachmentResult(
          path: savedPath,
          name: file.name,
          type: 'document',
          size: file.size,
        );
      }
    } catch (e) {
      debugPrint('خطأ في اختيار المستند: $e');
      rethrow;
    }
    return null;
  }

  // حفظ المرفق في مجلد التطبيق
  Future<String> _saveAttachment(String sourcePath, String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final attachmentsDir = Directory('${appDir.path}/attachments');

      if (!await attachmentsDir.exists()) {
        await attachmentsDir.create(recursive: true);
      }

      // إنشاء اسم ملف فريد
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(fileName);
      final nameWithoutExtension = path.basenameWithoutExtension(fileName);
      final uniqueFileName = '${nameWithoutExtension}_$timestamp$extension';

      final targetPath = '${attachmentsDir.path}/$uniqueFileName';

      // نسخ الملف
      final sourceFile = File(sourcePath);
      await sourceFile.copy(targetPath);

      return targetPath;
    } catch (e) {
      debugPrint('خطأ في حفظ المرفق: $e');
      rethrow;
    }
  }

  // حذف مرفق
  Future<bool> deleteAttachment(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في حذف المرفق: $e');
    }
    return false;
  }

  // التحقق من وجود المرفق
  Future<bool> attachmentExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  // الحصول على حجم المرفق
  Future<int> getAttachmentSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على حجم المرفق: $e');
    }
    return 0;
  }

  // تنسيق حجم الملف
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // الحصول على أيقونة حسب نوع الملف
  IconData getFileIcon(String fileName) {
    final extension = path.extension(fileName).toLowerCase();
    switch (extension) {
      case '.pdf':
        return Icons.picture_as_pdf;
      case '.doc':
      case '.docx':
        return Icons.description;
      case '.xls':
      case '.xlsx':
        return Icons.table_chart;
      case '.txt':
        return Icons.text_snippet;
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
        return Icons.image;
      default:
        return Icons.attach_file;
    }
  }

  // تنظيف المرفقات القديمة (اختياري)
  Future<void> cleanupOldAttachments({int daysOld = 30}) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final attachmentsDir = Directory('${appDir.path}/attachments');

      if (await attachmentsDir.exists()) {
        final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

        await for (final entity in attachmentsDir.list()) {
          if (entity is File) {
            final stat = await entity.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await entity.delete();
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف المرفقات القديمة: $e');
    }
  }
}

// نموذج نتيجة المرفق
class AttachmentResult {
  final String path;
  final String name;
  final String type; // 'image' أو 'document'
  final int size;

  AttachmentResult({
    required this.path,
    required this.name,
    required this.type,
    required this.size,
  });

  bool get isImage => type == 'image';
  bool get isDocument => type == 'document';
}
