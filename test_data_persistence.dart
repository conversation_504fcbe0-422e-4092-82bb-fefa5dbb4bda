import 'package:flutter/material.dart';
import 'lib/services/app_state_service.dart';
import 'lib/models/account.dart';
import 'lib/models/transaction.dart';

/// ملف اختبار لفحص حفظ البيانات
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 بدء اختبار حفظ البيانات...');
  
  final appState = AppStateService();
  
  try {
    // تحميل البيانات الموجودة
    await appState.loadAllData();
    print('✅ تم تحميل البيانات الموجودة');
    print('📊 عدد الحسابات الموجودة: ${appState.accounts.length}');
    print('📊 عدد المعاملات الموجودة: ${appState.transactions.length}');
    
    // إضافة حساب تجريبي
    final testAccount = Account(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}',
      name: 'ح<PERSON><PERSON><PERSON> اختبار حفظ البيانات',
      phoneNumber: '**********',
      address: 'عنوان تجريبي',
      balance: 1000.0,
      initialBalance: 1000.0,
      initialIsPositive: true,
      transactionCount: 0,
      isPositive: true,
      notes: 'هذا حساب تجريبي لاختبار حفظ البيانات',
      createdAt: DateTime.now(),
    );
    
    print('➕ إضافة حساب تجريبي...');
    final accountAdded = await appState.addAccount(testAccount);
    
    if (accountAdded) {
      print('✅ تم إضافة الحساب التجريبي بنجاح');
      
      // إضافة معاملة تجريبية
      final testTransaction = Transaction(
        id: 'test_trans_${DateTime.now().millisecondsSinceEpoch}',
        accountId: testAccount.id,
        description: 'معاملة اختبار حفظ البيانات',
        amount: 500.0,
        date: DateTime.now(),
        type: TransactionType.credit,
        notes: 'معاملة تجريبية لاختبار الحفظ',
      );
      
      print('➕ إضافة معاملة تجريبية...');
      final transactionAdded = await appState.addTransaction(testTransaction);
      
      if (transactionAdded) {
        print('✅ تم إضافة المعاملة التجريبية بنجاح');
        
        // التحقق من حفظ البيانات
        await appState.saveAllDataImmediately();
        print('💾 تم حفظ البيانات فوراً');
        
        // إعادة تحميل البيانات للتأكد من الحفظ
        await appState.loadAllData();
        print('🔄 تم إعادة تحميل البيانات');
        
        // التحقق من وجود البيانات
        final accountExists = appState.accounts.any((a) => a.id == testAccount.id);
        final transactionExists = appState.transactions.any((t) => t.id == testTransaction.id);
        
        if (accountExists && transactionExists) {
          print('🎉 نجح الاختبار! البيانات محفوظة بشكل صحيح');
          print('📊 إجمالي الحسابات: ${appState.accounts.length}');
          print('📊 إجمالي المعاملات: ${appState.transactions.length}');
        } else {
          print('❌ فشل الاختبار! البيانات لم تُحفظ بشكل صحيح');
          print('🔍 الحساب موجود: $accountExists');
          print('🔍 المعاملة موجودة: $transactionExists');
        }
      } else {
        print('❌ فشل في إضافة المعاملة التجريبية');
      }
    } else {
      print('❌ فشل في إضافة الحساب التجريبي');
    }
    
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
  }
  
  print('🏁 انتهى اختبار حفظ البيانات');
}
